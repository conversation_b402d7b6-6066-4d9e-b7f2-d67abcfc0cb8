/**
 * SliderComponent - Manages individual range-slider elements
 * Handles slider interactions, tooltips, and value synchronization
 */
import { eventBus } from '../core/EventBus.js';
import { portfolioState } from '../core/PortfolioState.js';

export class SliderComponent {
  constructor(sliderElement) {
    this.slider = sliderElement;
    this.assetName = sliderElement.dataset.asset;
    this.card = sliderElement.closest('.asset-card');
    this.valueDisplay = this.card.querySelector('.allocation-value');
    
    this.isInteracting = false;
    this.tooltip = null;
    
    this.init();
  }

  /**
   * Initialize the slider component
   */
  init() {
    this.setupEventListeners();
    this.createTooltip();
    this.updateDisplay();
  }

  /**
   * Setup event listeners for the slider
   */
  setupEventListeners() {
    // Range slider events
    this.slider.addEventListener('input', this.handleInput.bind(this));
    this.slider.addEventListener('change', this.handleChange.bind(this));
    
    // Mouse events for tooltip
    this.slider.addEventListener('mouseenter', this.showTooltip.bind(this));
    this.slider.addEventListener('mouseleave', this.hideTooltip.bind(this));
    
    // Touch events
    this.slider.addEventListener('touchstart', this.handleTouchStart.bind(this));
    this.slider.addEventListener('touchend', this.handleTouchEnd.bind(this));
    
    // Focus events for accessibility
    this.slider.addEventListener('focus', this.showTooltip.bind(this));
    this.slider.addEventListener('blur', this.hideTooltip.bind(this));
    
    // Prevent drag on slider container
    const container = this.slider.closest('.slider-container');
    if (container) {
      container.addEventListener('mousedown', (e) => e.stopPropagation());
      container.addEventListener('touchstart', (e) => e.stopPropagation());
    }

    // Listen for state changes
    eventBus.on('allocation:changed', this.handleStateChange.bind(this));
    eventBus.on('allocation:reset', this.handleReset.bind(this));
  }

  /**
   * Handle slider input (real-time changes)
   * @param {Event} e - Input event
   */
  handleInput(e) {
    e.stopPropagation();
    this.isInteracting = true;
    
    const rawValue = parseFloat(this.slider.value);
    const availableBudget = portfolioState.getAvailableBudget(this.assetName);
    const constrainedValue = Math.min(rawValue, availableBudget);
    
    // Update slider if constrained
    if (constrainedValue !== rawValue) {
      this.slider.value = constrainedValue;
      this.showBudgetWarning();
    }
    
    // Update state (skip validation since we already validated)
    portfolioState.setAllocation(this.assetName, constrainedValue, true);
    
    // Update tooltip
    this.updateTooltip(constrainedValue);
    
    // Trigger visual correlation
    this.triggerVisualCorrelation();
  }

  /**
   * Handle slider change (final value)
   * @param {Event} e - Change event
   */
  handleChange(e) {
    e.stopPropagation();
    this.isInteracting = false;
    
    const value = parseFloat(this.slider.value);
    portfolioState.setAllocation(this.assetName, value);
  }

  /**
   * Handle touch start
   * @param {Event} e - Touch event
   */
  handleTouchStart(e) {
    e.stopPropagation();
    this.isInteracting = true;
    this.showTooltip();
  }

  /**
   * Handle touch end
   * @param {Event} e - Touch event
   */
  handleTouchEnd(e) {
    this.isInteracting = false;
    setTimeout(() => {
      if (!this.isInteracting) {
        this.hideTooltip();
      }
    }, 100);
  }

  /**
   * Handle state changes from other components
   * @param {Object} data - State change data
   */
  handleStateChange(data) {
    if (data.assetName === this.assetName) {
      this.updateDisplay();
      this.updateCardState();
    }
  }

  /**
   * Handle reset event
   */
  handleReset() {
    this.slider.value = 0;
    this.updateDisplay();
    this.updateCardState();
  }

  /**
   * Update display elements
   */
  updateDisplay() {
    const percentage = portfolioState.getAllocation(this.assetName);
    const currencyValue = portfolioState.getCurrencyValue(percentage);
    
    // Update slider value
    this.slider.value = percentage;
    
    // Update value display
    this.valueDisplay.textContent = portfolioState.formatCurrency(currencyValue);
    
    // Update visual state
    this.updateCardState();
  }

  /**
   * Update card visual state
   */
  updateCardState() {
    const percentage = portfolioState.getAllocation(this.assetName);
    const isActive = percentage > 0;
    
    // Update card classes
    this.card.classList.toggle('active', isActive);
    
    // Update allocation level classes
    this.card.classList.remove('low-allocation', 'medium-allocation', 'high-allocation');
    if (percentage > 0) {
      if (percentage < 15) {
        this.card.classList.add('low-allocation');
      } else if (percentage < 40) {
        this.card.classList.add('medium-allocation');
      } else {
        this.card.classList.add('high-allocation');
      }
    }
    
    // Update value display state
    this.valueDisplay.classList.toggle('has-value', isActive);
  }

  /**
   * Create tooltip element
   */
  createTooltip() {
    this.tooltip = document.createElement('div');
    this.tooltip.className = 'modern-tooltip';
    this.tooltip.innerHTML = `
      <span class="tooltip-content">0%</span>
      <div class="tooltip-arrow"></div>
    `;
    document.body.appendChild(this.tooltip);
    
    // Setup FloatingUI
    this.setupFloatingUI();
  }

  /**
   * Setup FloatingUI for tooltip positioning
   */
  setupFloatingUI() {
    if (typeof FloatingUIDOM === 'undefined') {
      console.warn('FloatingUI not available');
      return;
    }

    this.cleanupFloatingUI = FloatingUIDOM.autoUpdate(this.slider, this.tooltip, () => {
      FloatingUIDOM.computePosition(this.slider, this.tooltip, {
        placement: 'top',
        middleware: [
          FloatingUIDOM.offset(12),
          FloatingUIDOM.flip(),
          FloatingUIDOM.shift({ padding: 8 }),
          FloatingUIDOM.arrow({
            element: this.tooltip.querySelector('.tooltip-arrow'),
          }),
        ],
      }).then(({ x, y, placement, middlewareData }) => {
        Object.assign(this.tooltip.style, {
          left: `${x}px`,
          top: `${y}px`,
        });

        this.tooltip.setAttribute('data-placement', placement);

        // Position arrow
        const arrow = this.tooltip.querySelector('.tooltip-arrow');
        if (middlewareData.arrow) {
          const { x: arrowX, y: arrowY } = middlewareData.arrow;
          Object.assign(arrow.style, {
            left: arrowX != null ? `${arrowX}px` : '',
            top: arrowY != null ? `${arrowY}px` : '',
          });
        }
      });
    });
  }

  /**
   * Show tooltip
   */
  showTooltip() {
    if (!this.tooltip) return;
    
    const percentage = portfolioState.getAllocation(this.assetName);
    this.updateTooltip(percentage);
    this.tooltip.classList.add('show');
  }

  /**
   * Hide tooltip
   */
  hideTooltip() {
    if (!this.tooltip || this.isInteracting) return;
    
    this.tooltip.classList.remove('show');
  }

  /**
   * Update tooltip content
   * @param {number} percentage - Current percentage
   */
  updateTooltip(percentage) {
    if (!this.tooltip) return;
    
    const content = this.tooltip.querySelector('.tooltip-content');
    const currencyValue = portfolioState.getCurrencyValue(percentage);
    
    content.innerHTML = `${percentage.toFixed(1)}%<br><small>${portfolioState.formatCurrency(currencyValue)}</small>`;
  }

  /**
   * Show budget warning
   */
  showBudgetWarning() {
    if (!this.tooltip) return;
    
    this.tooltip.className = 'modern-tooltip error';
    const content = this.tooltip.querySelector('.tooltip-content');
    content.textContent = 'Limite de orçamento excedido';
    
    this.tooltip.classList.add('show');
    
    setTimeout(() => {
      this.tooltip.className = 'modern-tooltip';
      this.updateTooltip(portfolioState.getAllocation(this.assetName));
    }, 2000);
  }

  /**
   * Trigger visual correlation effect
   */
  triggerVisualCorrelation() {
    eventBus.emit('slider:correlation', { sourceAsset: this.assetName });
  }

  /**
   * Cleanup component
   */
  destroy() {
    if (this.cleanupFloatingUI) {
      this.cleanupFloatingUI();
    }
    
    if (this.tooltip && this.tooltip.parentNode) {
      this.tooltip.parentNode.removeChild(this.tooltip);
    }
  }
}

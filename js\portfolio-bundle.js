/**
 * Portfolio Allocation Interface - Bundled Version
 * Clean, modern implementation without ES6 modules to avoid CORS issues
 * Built specifically for range-slider-element and FloatingUI
 */

// ============================================================================
// EventBus - Centralized event system for component communication
// ============================================================================
class EventBus {
  constructor() {
    this.events = new Map();
  }

  on(eventName, callback) {
    if (!this.events.has(eventName)) {
      this.events.set(eventName, new Set());
    }

    this.events.get(eventName).add(callback);

    return () => {
      const callbacks = this.events.get(eventName);
      if (callbacks) {
        callbacks.delete(callback);
        if (callbacks.size === 0) {
          this.events.delete(eventName);
        }
      }
    };
  }

  once(eventName, callback) {
    const unsubscribe = this.on(eventName, (...args) => {
      unsubscribe();
      callback(...args);
    });
    return unsubscribe;
  }

  emit(eventName, ...args) {
    const callbacks = this.events.get(eventName);
    if (callbacks) {
      callbacks.forEach((callback) => {
        try {
          callback(...args);
        } catch (error) {
          console.error(`Error in event callback for ${eventName}:`, error);
        }
      });
    }
  }

  off(eventName) {
    this.events.delete(eventName);
  }

  clear() {
    this.events.clear();
  }

  getActiveEvents() {
    return Array.from(this.events.keys());
  }

  getListenerCount(eventName) {
    const callbacks = this.events.get(eventName);
    return callbacks ? callbacks.size : 0;
  }
}

// ============================================================================
// PortfolioState - Centralized state management for portfolio allocations
// ============================================================================
class PortfolioState {
  constructor() {
    this.totalCapital = 980250.0;
    this.allocations = new Map();
    this.snapPoints = [
      0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90,
      95, 100,
    ];

    this.initializeAssets();
  }

  initializeAssets() {
    const sliders = document.querySelectorAll("range-slider.allocation-slider");
    sliders.forEach((slider) => {
      const assetName = slider.dataset.asset;
      if (assetName) {
        this.allocations.set(assetName, 0);
      }
    });
  }

  setAllocation(assetName, percentage, skipValidation = false) {
    const oldValue = this.allocations.get(assetName) || 0;

    if (!skipValidation) {
      const availableBudget = this.getAvailableBudget(assetName);
      percentage = Math.min(percentage, availableBudget);
    }

    percentage = this.snapToNearestPoint(percentage);
    this.allocations.set(assetName, percentage);

    window.eventBus.emit("allocation:changed", {
      assetName,
      oldValue,
      newValue: percentage,
      allocations: this.getAllocations(),
      totalAllocated: this.getTotalAllocated(),
      remainingBudget: this.getRemainingBudget(),
    });
  }

  getAllocation(assetName) {
    return this.allocations.get(assetName) || 0;
  }

  getAllocations() {
    return Object.fromEntries(this.allocations);
  }

  getTotalAllocated() {
    return Array.from(this.allocations.values()).reduce(
      (sum, val) => sum + val,
      0
    );
  }

  getRemainingBudget() {
    return Math.max(0, 100 - this.getTotalAllocated());
  }

  getAvailableBudget(assetName) {
    let totalOthers = 0;
    for (const [asset, allocation] of this.allocations) {
      if (asset !== assetName) {
        totalOthers += allocation;
      }
    }
    return Math.max(0, 100 - totalOthers);
  }

  snapToNearestPoint(value) {
    let closest = this.snapPoints[0];
    let minDiff = Math.abs(value - closest);

    for (let point of this.snapPoints) {
      const diff = Math.abs(value - point);
      if (diff < minDiff) {
        minDiff = diff;
        closest = point;
      }
    }

    return closest;
  }

  resetAll() {
    const oldAllocations = this.getAllocations();

    for (const assetName of this.allocations.keys()) {
      this.allocations.set(assetName, 0);
    }

    window.eventBus.emit("allocation:reset", {
      oldAllocations,
      newAllocations: this.getAllocations(),
    });
  }

  getCurrencyValue(percentage) {
    return (this.totalCapital * percentage) / 100;
  }

  getPercentageFromCurrency(currencyValue) {
    return (currencyValue / this.totalCapital) * 100;
  }

  formatCurrency(value) {
    if (value === 0) return "R$0";

    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  }

  getSummary() {
    return {
      totalCapital: this.totalCapital,
      totalAllocated: this.getTotalAllocated(),
      remainingBudget: this.getRemainingBudget(),
      allocations: this.getAllocations(),
      currencyAllocations: Object.fromEntries(
        Array.from(this.allocations.entries()).map(([asset, percentage]) => [
          asset,
          this.getCurrencyValue(percentage),
        ])
      ),
    };
  }
}

// ============================================================================
// SliderComponent - Manages individual range-slider elements
// ============================================================================
class SliderComponent {
  constructor(sliderElement) {
    this.slider = sliderElement;
    this.assetName = sliderElement.dataset.asset;
    this.card = sliderElement.closest(".asset-card");
    this.valueDisplay = this.card.querySelector(".allocation-value");

    this.isInteracting = false;
    this.tooltip = null;

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.createTooltip();
    this.updateDisplay();
  }

  setupEventListeners() {
    this.slider.addEventListener("input", this.handleInput.bind(this));
    this.slider.addEventListener("change", this.handleChange.bind(this));

    this.slider.addEventListener("mouseenter", this.showTooltip.bind(this));
    this.slider.addEventListener("mouseleave", this.hideTooltip.bind(this));

    this.slider.addEventListener(
      "touchstart",
      this.handleTouchStart.bind(this)
    );
    this.slider.addEventListener("touchend", this.handleTouchEnd.bind(this));

    this.slider.addEventListener("focus", this.showTooltip.bind(this));
    this.slider.addEventListener("blur", this.hideTooltip.bind(this));

    const container = this.slider.closest(".slider-container");
    if (container) {
      container.addEventListener("mousedown", (e) => e.stopPropagation());
      container.addEventListener("touchstart", (e) => e.stopPropagation());
    }

    window.eventBus.on("allocation:changed", this.handleStateChange.bind(this));
    window.eventBus.on("allocation:reset", this.handleReset.bind(this));
  }

  handleInput(e) {
    e.stopPropagation();
    this.isInteracting = true;

    const rawValue = parseFloat(this.slider.value);
    const availableBudget = window.portfolioState.getAvailableBudget(
      this.assetName
    );
    const constrainedValue = Math.min(rawValue, availableBudget);

    if (constrainedValue !== rawValue) {
      this.slider.value = constrainedValue;
      this.showBudgetWarning();
    }

    window.portfolioState.setAllocation(this.assetName, constrainedValue, true);
    this.updateTooltip(constrainedValue);
    this.triggerVisualCorrelation();
  }

  handleChange(e) {
    e.stopPropagation();
    this.isInteracting = false;

    const value = parseFloat(this.slider.value);
    window.portfolioState.setAllocation(this.assetName, value);
  }

  handleTouchStart(e) {
    e.stopPropagation();
    this.isInteracting = true;
    this.showTooltip();
  }

  handleTouchEnd(e) {
    this.isInteracting = false;
    setTimeout(() => {
      if (!this.isInteracting) {
        this.hideTooltip();
      }
    }, 100);
  }

  handleStateChange(data) {
    if (data.assetName === this.assetName) {
      this.updateDisplay();
      this.updateCardState();
    }
  }

  handleReset() {
    this.slider.value = 0;
    this.updateDisplay();
    this.updateCardState();
  }

  updateDisplay() {
    const percentage = window.portfolioState.getAllocation(this.assetName);
    const currencyValue = window.portfolioState.getCurrencyValue(percentage);

    this.slider.value = percentage;
    this.valueDisplay.textContent =
      window.portfolioState.formatCurrency(currencyValue);
    this.updateCardState();
  }

  updateCardState() {
    const percentage = window.portfolioState.getAllocation(this.assetName);
    const isActive = percentage > 0;

    this.card.classList.toggle("active", isActive);

    this.card.classList.remove(
      "low-allocation",
      "medium-allocation",
      "high-allocation"
    );
    if (percentage > 0) {
      if (percentage < 15) {
        this.card.classList.add("low-allocation");
      } else if (percentage < 40) {
        this.card.classList.add("medium-allocation");
      } else {
        this.card.classList.add("high-allocation");
      }
    }

    this.valueDisplay.classList.toggle("has-value", isActive);
  }

  createTooltip() {
    this.tooltip = document.createElement("div");
    this.tooltip.className = "modern-tooltip";
    this.tooltip.innerHTML = `
      <span class="tooltip-content">0%</span>
      <div class="tooltip-arrow"></div>
    `;
    document.body.appendChild(this.tooltip);

    this.setupFloatingUI();
  }

  setupFloatingUI() {
    if (typeof FloatingUIDOM === "undefined") {
      console.warn("FloatingUI not available");
      return;
    }

    this.cleanupFloatingUI = FloatingUIDOM.autoUpdate(
      this.slider,
      this.tooltip,
      () => {
        FloatingUIDOM.computePosition(this.slider, this.tooltip, {
          placement: "top",
          middleware: [
            FloatingUIDOM.offset(12),
            FloatingUIDOM.flip(),
            FloatingUIDOM.shift({ padding: 8 }),
            FloatingUIDOM.arrow({
              element: this.tooltip.querySelector(".tooltip-arrow"),
            }),
          ],
        }).then(({ x, y, placement, middlewareData }) => {
          Object.assign(this.tooltip.style, {
            left: `${x}px`,
            top: `${y}px`,
          });

          this.tooltip.setAttribute("data-placement", placement);

          const arrow = this.tooltip.querySelector(".tooltip-arrow");
          if (middlewareData.arrow) {
            const { x: arrowX, y: arrowY } = middlewareData.arrow;
            Object.assign(arrow.style, {
              left: arrowX != null ? `${arrowX}px` : "",
              top: arrowY != null ? `${arrowY}px` : "",
            });
          }
        });
      }
    );
  }

  showTooltip() {
    if (!this.tooltip) return;

    const percentage = window.portfolioState.getAllocation(this.assetName);
    this.updateTooltip(percentage);
    this.tooltip.classList.add("show");
  }

  hideTooltip() {
    if (!this.tooltip || this.isInteracting) return;

    this.tooltip.classList.remove("show");
  }

  updateTooltip(percentage) {
    if (!this.tooltip) return;

    const content = this.tooltip.querySelector(".tooltip-content");
    const currencyValue = window.portfolioState.getCurrencyValue(percentage);

    content.innerHTML = `${percentage.toFixed(
      1
    )}%<br><small>${window.portfolioState.formatCurrency(
      currencyValue
    )}</small>`;
  }

  showBudgetWarning() {
    if (!this.tooltip) return;

    this.tooltip.className = "modern-tooltip error";
    const content = this.tooltip.querySelector(".tooltip-content");
    content.textContent = "Limite de orçamento excedido";

    this.tooltip.classList.add("show");

    setTimeout(() => {
      this.tooltip.className = "modern-tooltip";
      this.updateTooltip(window.portfolioState.getAllocation(this.assetName));
    }, 2000);
  }

  triggerVisualCorrelation() {
    window.eventBus.emit("slider:correlation", { sourceAsset: this.assetName });
  }

  destroy() {
    if (this.cleanupFloatingUI) {
      this.cleanupFloatingUI();
    }

    if (this.tooltip && this.tooltip.parentNode) {
      this.tooltip.parentNode.removeChild(this.tooltip);
    }
  }
}

// ============================================================================
// DisplayManager - Manages display updates and visual feedback
// ============================================================================
class DisplayManager {
  constructor() {
    this.remainingValueElement = document.querySelector(".remaining-value");
    this.strategyTextElement = document.getElementById("strategy-text");
    this.remainingSection = document.querySelector(".remaining-section");

    this.strategies = this.initializeStrategies();
    this.currentStrategy = null;

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.updateAllDisplays();
  }

  setupEventListeners() {
    window.eventBus.on(
      "allocation:changed",
      this.handleAllocationChange.bind(this)
    );
    window.eventBus.on("allocation:reset", this.handleReset.bind(this));
  }

  handleAllocationChange(data) {
    this.updateRemainingAmount(data.remainingBudget);
    this.updateStrategyText(data.allocations);
  }

  handleReset() {
    this.updateAllDisplays();
  }

  updateAllDisplays() {
    const summary = window.portfolioState.getSummary();
    this.updateRemainingAmount(summary.remainingBudget);
    this.updateStrategyText(summary.allocations);
  }

  updateRemainingAmount(remainingPercentage) {
    if (!this.remainingValueElement) return;

    const remainingValue =
      window.portfolioState.getCurrencyValue(remainingPercentage);
    this.remainingValueElement.textContent =
      window.portfolioState.formatCurrency(remainingValue);

    if (this.remainingSection) {
      this.remainingSection.classList.remove(
        "no-remaining",
        "low-remaining",
        "high-remaining"
      );

      if (remainingPercentage === 0) {
        this.remainingSection.classList.add("no-remaining");
      } else if (remainingPercentage < 10) {
        this.remainingSection.classList.add("low-remaining");
      } else if (remainingPercentage > 50) {
        this.remainingSection.classList.add("high-remaining");
      }
    }
  }

  updateStrategyText(allocations) {
    if (!this.strategyTextElement) return;

    const strategy = this.determineStrategy(allocations);

    if (this.currentStrategy !== strategy) {
      this.currentStrategy = strategy;
      this.animateStrategyChange(strategy);
    }
  }

  determineStrategy(allocations) {
    const matchingStrategies = Object.entries(this.strategies)
      .filter(([key, strategy]) => strategy.condition(allocations))
      .sort((a, b) => a[1].priority - b[1].priority);

    return matchingStrategies.length > 0
      ? matchingStrategies[0][1]
      : this.strategies.default;
  }

  animateStrategyChange(strategy) {
    this.strategyTextElement.style.opacity = "0.3";
    this.strategyTextElement.style.transform = "translateY(5px)";

    setTimeout(() => {
      this.strategyTextElement.innerHTML = `
        <div class="strategy-content">
          <span class="strategy-icon" style="color: ${strategy.color}">${strategy.icon}</span>
          <span class="strategy-text">${strategy.text}</span>
        </div>
      `;

      this.strategyTextElement.setAttribute(
        "data-strategy-type",
        this.getStrategyType(strategy)
      );

      this.strategyTextElement.style.opacity = "1";
      this.strategyTextElement.style.transform = "translateY(0)";
    }, 200);
  }

  getStrategyType(strategy) {
    if (strategy.text.includes("conservador")) return "conservative";
    if (strategy.text.includes("agressivo")) return "aggressive";
    if (strategy.text.includes("equilibrado")) return "balanced";
    if (strategy.text.includes("liquidez")) return "liquidity";
    if (strategy.text.includes("diversificação")) return "diversified";
    return "default";
  }

  initializeStrategies() {
    return {
      ultraConservative: {
        priority: 1,
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, [
            "renda-fixa",
          ]);
          const savings = this.getAssetTotal(allocations, "Poupança");
          return fixedIncome + savings > 80 && fixedIncome > 60;
        },
        text: "Perfil ultra-conservador - Máxima segurança com foco em preservação de capital.",
        icon: "🛡️",
        color: "#10b981",
      },

      conservative: {
        priority: 2,
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, [
            "renda-fixa",
          ]);
          const liquidityFunds = this.getAssetTotal(allocations, "Liquidez");
          return fixedIncome > 50 && fixedIncome <= 80 && liquidityFunds > 15;
        },
        text: "Perfil conservador - Priorizando segurança e liquidez com foco em renda fixa.",
        icon: "🏦",
        color: "#3b82f6",
      },

      balanced: {
        priority: 3,
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, [
            "renda-fixa",
          ]);
          const equity = this.getCategoryTotal(allocations, ["fundos"]);
          return (
            fixedIncome >= 30 &&
            fixedIncome <= 60 &&
            equity >= 20 &&
            equity <= 50
          );
        },
        text: "Perfil equilibrado - Diversificação balanceada entre renda fixa e variável.",
        icon: "⚖️",
        color: "#f59e0b",
      },

      aggressive: {
        priority: 4,
        condition: (allocations) => {
          const equity = this.getCategoryTotal(allocations, ["fundos"]);
          const fixedIncome = this.getCategoryTotal(allocations, [
            "renda-fixa",
          ]);
          return equity > 60 && fixedIncome < 30;
        },
        text: "Perfil agressivo - Foco em crescimento de longo prazo com alta exposição a renda variável.",
        icon: "🚀",
        color: "#ef4444",
      },

      liquidityFocused: {
        priority: 5,
        condition: (allocations) => {
          const liquidityFunds = this.getAssetTotal(allocations, "Liquidez");
          const savings = this.getAssetTotal(allocations, "Poupança");
          return liquidityFunds > 30 || savings > 25;
        },
        text: "Estratégia de liquidez - Mantendo alta reserva para oportunidades e emergências.",
        icon: "💧",
        color: "#06b6d4",
      },

      diversified: {
        priority: 6,
        condition: (allocations) => {
          const categories = ["renda-fixa", "fundos", "outros"];
          const activeCats = categories.filter(
            (cat) => this.getCategoryTotal(allocations, [cat]) > 10
          );
          return activeCats.length >= 3;
        },
        text: "Alta diversificação - Distribuindo riscos entre múltiplas classes de ativos.",
        icon: "🌐",
        color: "#6366f1",
      },

      default: {
        priority: 999,
        condition: () => true,
        text: "Continue alocando para descobrir sua estratégia de investimento personalizada.",
        icon: "💡",
        color: "#6b7280",
      },
    };
  }

  getCategoryTotal(allocations, categories) {
    let total = 0;
    for (const [asset, value] of Object.entries(allocations)) {
      const card = document.querySelector(`[data-asset="${asset}"]`);
      if (card) {
        const category = card.dataset.category;
        if (categories.includes(category)) {
          total += value;
        }
      }
    }
    return total;
  }

  getAssetTotal(allocations, assetType) {
    let total = 0;
    for (const [asset, value] of Object.entries(allocations)) {
      const card = document.querySelector(`[data-asset="${asset}"]`);
      if (card) {
        const cardAssetType =
          card.querySelector(".asset-type")?.textContent?.trim() ||
          card.querySelector(".asset-type-text")?.textContent?.trim();
        if (cardAssetType === assetType) {
          total += value;
        }
      }
    }
    return total;
  }
}

// ============================================================================
// CorrelationManager - Handles visual correlation effects between sliders
// ============================================================================
class CorrelationManager {
  constructor() {
    this.isEnabled = true;
    this.effectIntensity = 0.02;
    this.effectDuration = 300;
    this.activeEffects = new Set();

    this.init();
  }

  init() {
    this.setupEventListeners();
  }

  setupEventListeners() {
    window.eventBus.on(
      "slider:correlation",
      this.handleCorrelationTrigger.bind(this)
    );
  }

  handleCorrelationTrigger(data) {
    if (!this.isEnabled) return;

    this.triggerVisualCorrelation(data.sourceAsset);
  }

  triggerVisualCorrelation(sourceAsset) {
    const allSliders = document.querySelectorAll(
      "range-slider.allocation-slider"
    );
    const otherSliders = Array.from(allSliders).filter(
      (slider) => slider.dataset.asset !== sourceAsset
    );

    if (otherSliders.length === 0) return;

    const numCorrelated = Math.min(2, otherSliders.length);
    const correlatedSliders = this.getRandomSliders(
      otherSliders,
      numCorrelated
    );

    correlatedSliders.forEach((slider, index) => {
      const card = slider.closest(".asset-card");
      const delay = index * 50;

      setTimeout(() => {
        this.applyCorrelationEffect(card, slider.dataset.asset);
      }, delay);
    });
  }

  applyCorrelationEffect(card, assetName) {
    if (this.activeEffects.has(assetName)) return;

    this.activeEffects.add(assetName);

    const originalTransform = card.style.transform;
    const originalBoxShadow = card.style.boxShadow;
    const originalBorderColor = card.style.borderColor;
    const originalTransition = card.style.transition;

    card.style.transform = "scale(1.02) translateY(-2px)";
    card.style.transition =
      "transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease";
    card.style.boxShadow = "0 8px 25px rgba(196, 151, 37, 0.15)";
    card.style.borderColor = "rgba(196, 151, 37, 0.3)";

    this.addCorrelationIndicator(card);

    setTimeout(() => {
      card.style.transform = originalTransform;
      card.style.boxShadow = originalBoxShadow;
      card.style.borderColor = originalBorderColor;
      card.style.transition = originalTransition;

      this.removeCorrelationIndicator(card);
      this.activeEffects.delete(assetName);
    }, this.effectDuration);
  }

  addCorrelationIndicator(card) {
    const indicator = document.createElement("div");
    indicator.className = "correlation-indicator";
    indicator.innerHTML = "↗️";
    indicator.style.cssText = `
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 12px;
      opacity: 0.7;
      animation: correlationPulse 0.3s ease;
      z-index: 10;
    `;

    card.style.position = "relative";
    card.appendChild(indicator);
  }

  removeCorrelationIndicator(card) {
    const indicator = card.querySelector(".correlation-indicator");
    if (indicator) {
      indicator.remove();
    }
  }

  getRandomSliders(sliders, count) {
    const shuffled = [...sliders].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  enable() {
    this.isEnabled = true;
  }

  disable() {
    this.isEnabled = false;
  }

  setIntensity(intensity) {
    this.effectIntensity = Math.max(0, Math.min(1, intensity));
  }

  setDuration(duration) {
    this.effectDuration = Math.max(100, duration);
  }

  getSettings() {
    return {
      enabled: this.isEnabled,
      intensity: this.effectIntensity,
      duration: this.effectDuration,
      activeEffects: this.activeEffects.size,
    };
  }
}

// ============================================================================
// PortfolioApp - Main Application Class
// ============================================================================
class PortfolioApp {
  constructor() {
    this.sliders = new Map();
    this.displayManager = null;
    this.correlationManager = null;
    this.isInitialized = false;
  }

  async init() {
    try {
      console.log("Initializing Portfolio App...");

      this.showLoadingIndicator();

      await this.waitForCustomElements();

      this.initializeSliders();
      this.displayManager = new DisplayManager();
      this.correlationManager = new CorrelationManager();

      this.setupGlobalEventListeners();
      this.updateAllDisplays();

      this.hideLoadingIndicator();

      this.isInitialized = true;
      console.log("Portfolio App initialized successfully");

      this.showWelcomeMessage();
    } catch (error) {
      console.error("Failed to initialize Portfolio App:", error);
      this.showErrorMessage(
        "Falha ao inicializar a aplicação. Recarregue a página."
      );
    }
  }

  async waitForCustomElements() {
    if (customElements.get("range-slider")) {
      return Promise.resolve();
    }

    return customElements.whenDefined("range-slider");
  }

  initializeSliders() {
    const sliderElements = document.querySelectorAll(
      "range-slider.allocation-slider"
    );

    sliderElements.forEach((sliderElement) => {
      const assetName = sliderElement.dataset.asset;
      if (assetName) {
        const sliderComponent = new SliderComponent(sliderElement);
        this.sliders.set(assetName, sliderComponent);
      }
    });

    console.log(`Initialized ${this.sliders.size} sliders`);
  }

  setupGlobalEventListeners() {
    document.addEventListener(
      "keydown",
      this.handleKeyboardShortcuts.bind(this)
    );
    window.addEventListener("resize", this.handleResize.bind(this));
    window.addEventListener("beforeunload", this.handleBeforeUnload.bind(this));
    document.addEventListener(
      "visibilitychange",
      this.handleVisibilityChange.bind(this)
    );
    window.addEventListener("error", this.handleError.bind(this));
    window.addEventListener(
      "unhandledrejection",
      this.handleUnhandledRejection.bind(this)
    );
  }

  handleKeyboardShortcuts(e) {
    if ((e.ctrlKey || e.metaKey) && e.key === "r") {
      e.preventDefault();
      this.resetAllAllocations();
    }

    if (e.key === "Escape") {
      this.hideAllTooltips();
    }
  }

  handleResize() {
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      this.handleResizeComplete();
    }, 250);
  }

  handleResizeComplete() {
    this.sliders.forEach((slider) => {
      if (slider.tooltip && slider.tooltip.classList.contains("show")) {
        slider.hideTooltip();
        setTimeout(() => slider.showTooltip(), 100);
      }
    });
  }

  handleBeforeUnload() {
    this.cleanup();
  }

  handleVisibilityChange() {
    if (document.hidden) {
      this.hideAllTooltips();
    }
  }

  handleError(e) {
    console.error("JavaScript error:", e.error);
    this.showErrorMessage(
      "Erro detectado. A aplicação pode não funcionar corretamente."
    );
  }

  handleUnhandledRejection(e) {
    console.error("Unhandled promise rejection:", e.reason);
    this.showErrorMessage("Erro inesperado detectado.");
  }

  updateAllDisplays() {
    if (this.displayManager) {
      this.displayManager.updateAllDisplays();
    }
  }

  resetAllAllocations() {
    if (confirm("Tem certeza que deseja resetar todas as alocações?")) {
      window.portfolioState.resetAll();
      this.showNotification("Todas as alocações foram resetadas", "info");
    }
  }

  hideAllTooltips() {
    this.sliders.forEach((slider) => {
      slider.hideTooltip();
    });
  }

  showLoadingIndicator() {
    const loadingDiv = document.createElement("div");
    loadingDiv.id = "app-loading";
    loadingDiv.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.95);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(2px);
      ">
        <div style="text-align: center; color: #1e293b;">
          <div style="
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #c49725;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
          "></div>
          <div style="font-size: 16px; font-weight: 500; color: #64748b;">
            Carregando interface...
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(loadingDiv);
  }

  hideLoadingIndicator() {
    const loadingDiv = document.getElementById("app-loading");
    if (loadingDiv) {
      loadingDiv.style.opacity = "0";
      loadingDiv.style.transition = "opacity 0.3s ease";

      setTimeout(() => {
        if (loadingDiv.parentNode) {
          loadingDiv.parentNode.removeChild(loadingDiv);
        }
      }, 300);
    }
  }

  showWelcomeMessage() {
    if (!localStorage.getItem("portfolio-welcome-shown")) {
      setTimeout(() => {
        this.showNotification(
          "Bem-vindo! Use os sliders para alocar seu patrimônio.",
          "info",
          5000
        );
        localStorage.setItem("portfolio-welcome-shown", "true");
      }, 1000);
    }
  }

  showNotification(message, type = "info", duration = 3000) {
    const notification = document.createElement("div");
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.transform = "translateX(0)";
    }, 10);

    setTimeout(() => {
      notification.style.transform = "translateX(100%)";
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, duration);
  }

  showErrorMessage(message) {
    this.showNotification(message, "error", 5000);
  }

  getState() {
    return {
      isInitialized: this.isInitialized,
      sliderCount: this.sliders.size,
      portfolioSummary: window.portfolioState.getSummary(),
      correlationSettings: this.correlationManager?.getSettings(),
    };
  }

  cleanup() {
    this.sliders.forEach((slider) => {
      slider.destroy();
    });
    this.sliders.clear();

    window.eventBus.clear();

    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
    }

    console.log("Portfolio App cleaned up");
  }
}

// ============================================================================
// Initialization and Global Setup
// ============================================================================

// Add correlation pulse animation to CSS
if (!document.querySelector("#correlation-styles")) {
  const style = document.createElement("style");
  style.id = "correlation-styles";
  style.textContent = `
    @keyframes correlationPulse {
      0% {
        opacity: 0;
        transform: scale(0.5);
      }
      50% {
        opacity: 1;
        transform: scale(1.2);
      }
      100% {
        opacity: 0.7;
        transform: scale(1);
      }
    }
  `;
  document.head.appendChild(style);
}

// Create global instances
window.eventBus = new EventBus();
window.portfolioState = new PortfolioState();

// Initialize app when DOM is ready
let app = null;

function initializeApp() {
  app = new PortfolioApp();
  app.init();

  // Make app available globally for debugging
  if (
    window.location.hostname === "localhost" ||
    window.location.search.includes("debug=true")
  ) {
    window.portfolioApp = app;
  }
}

if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initializeApp);
} else {
  initializeApp();
}

/**
 * DisplayManager - Manages display updates and visual feedback
 * Handles remaining amount, strategy text, and visual states
 */
import { eventBus } from '../core/EventBus.js';
import { portfolioState } from '../core/PortfolioState.js';

export class DisplayManager {
  constructor() {
    this.remainingValueElement = document.querySelector('.remaining-value');
    this.strategyTextElement = document.getElementById('strategy-text');
    this.remainingSection = document.querySelector('.remaining-section');
    
    this.strategies = this.initializeStrategies();
    this.currentStrategy = null;
    
    this.init();
  }

  /**
   * Initialize display manager
   */
  init() {
    this.setupEventListeners();
    this.updateAllDisplays();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    eventBus.on('allocation:changed', this.handleAllocationChange.bind(this));
    eventBus.on('allocation:reset', this.handleReset.bind(this));
  }

  /**
   * Handle allocation changes
   * @param {Object} data - Allocation change data
   */
  handleAllocationChange(data) {
    this.updateRemainingAmount(data.remainingBudget);
    this.updateStrategyText(data.allocations);
  }

  /**
   * Handle reset
   */
  handleReset() {
    this.updateAllDisplays();
  }

  /**
   * Update all displays
   */
  updateAllDisplays() {
    const summary = portfolioState.getSummary();
    this.updateRemainingAmount(summary.remainingBudget);
    this.updateStrategyText(summary.allocations);
  }

  /**
   * Update remaining amount display
   * @param {number} remainingPercentage - Remaining percentage
   */
  updateRemainingAmount(remainingPercentage) {
    if (!this.remainingValueElement) return;
    
    const remainingValue = portfolioState.getCurrencyValue(remainingPercentage);
    this.remainingValueElement.textContent = portfolioState.formatCurrency(remainingValue);
    
    // Update visual feedback
    if (this.remainingSection) {
      this.remainingSection.classList.remove('no-remaining', 'low-remaining', 'high-remaining');
      
      if (remainingPercentage === 0) {
        this.remainingSection.classList.add('no-remaining');
      } else if (remainingPercentage < 10) {
        this.remainingSection.classList.add('low-remaining');
      } else if (remainingPercentage > 50) {
        this.remainingSection.classList.add('high-remaining');
      }
    }
  }

  /**
   * Update strategy text
   * @param {Object} allocations - Current allocations
   */
  updateStrategyText(allocations) {
    if (!this.strategyTextElement) return;
    
    const strategy = this.determineStrategy(allocations);
    
    if (this.currentStrategy !== strategy) {
      this.currentStrategy = strategy;
      this.animateStrategyChange(strategy);
    }
  }

  /**
   * Determine current strategy based on allocations
   * @param {Object} allocations - Current allocations
   * @returns {Object} Strategy object
   */
  determineStrategy(allocations) {
    // Find matching strategies based on priority
    const matchingStrategies = Object.entries(this.strategies)
      .filter(([key, strategy]) => strategy.condition(allocations))
      .sort((a, b) => a[1].priority - b[1].priority);

    return matchingStrategies.length > 0 
      ? matchingStrategies[0][1] 
      : this.strategies.default;
  }

  /**
   * Animate strategy text change
   * @param {Object} strategy - Strategy object
   */
  animateStrategyChange(strategy) {
    // Fade out
    this.strategyTextElement.style.opacity = '0.3';
    this.strategyTextElement.style.transform = 'translateY(5px)';
    
    setTimeout(() => {
      // Update content
      this.strategyTextElement.innerHTML = `
        <div class="strategy-content">
          <span class="strategy-icon" style="color: ${strategy.color}">${strategy.icon}</span>
          <span class="strategy-text">${strategy.text}</span>
        </div>
      `;
      
      // Set strategy type for styling
      this.strategyTextElement.setAttribute('data-strategy-type', this.getStrategyType(strategy));
      
      // Fade in
      this.strategyTextElement.style.opacity = '1';
      this.strategyTextElement.style.transform = 'translateY(0)';
    }, 200);
  }

  /**
   * Get strategy type for CSS styling
   * @param {Object} strategy - Strategy object
   * @returns {string} Strategy type
   */
  getStrategyType(strategy) {
    if (strategy.text.includes('conservador')) return 'conservative';
    if (strategy.text.includes('agressivo')) return 'aggressive';
    if (strategy.text.includes('equilibrado')) return 'balanced';
    if (strategy.text.includes('liquidez')) return 'liquidity';
    if (strategy.text.includes('diversificação')) return 'diversified';
    return 'default';
  }

  /**
   * Initialize strategy definitions
   * @returns {Object} Strategy definitions
   */
  initializeStrategies() {
    return {
      ultraConservative: {
        priority: 1,
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, ['renda-fixa']);
          const savings = this.getAssetTotal(allocations, 'Poupança');
          return fixedIncome + savings > 80 && fixedIncome > 60;
        },
        text: 'Perfil ultra-conservador - Máxima segurança com foco em preservação de capital.',
        icon: '🛡️',
        color: '#10b981'
      },

      conservative: {
        priority: 2,
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, ['renda-fixa']);
          const liquidityFunds = this.getAssetTotal(allocations, 'Liquidez');
          return fixedIncome > 50 && fixedIncome <= 80 && liquidityFunds > 15;
        },
        text: 'Perfil conservador - Priorizando segurança e liquidez com foco em renda fixa.',
        icon: '🏦',
        color: '#3b82f6'
      },

      balanced: {
        priority: 3,
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, ['renda-fixa']);
          const equity = this.getCategoryTotal(allocations, ['fundos']);
          return fixedIncome >= 30 && fixedIncome <= 60 && equity >= 20 && equity <= 50;
        },
        text: 'Perfil equilibrado - Diversificação balanceada entre renda fixa e variável.',
        icon: '⚖️',
        color: '#f59e0b'
      },

      aggressive: {
        priority: 4,
        condition: (allocations) => {
          const equity = this.getCategoryTotal(allocations, ['fundos']);
          const fixedIncome = this.getCategoryTotal(allocations, ['renda-fixa']);
          return equity > 60 && fixedIncome < 30;
        },
        text: 'Perfil agressivo - Foco em crescimento de longo prazo com alta exposição a renda variável.',
        icon: '🚀',
        color: '#ef4444'
      },

      liquidityFocused: {
        priority: 5,
        condition: (allocations) => {
          const liquidityFunds = this.getAssetTotal(allocations, 'Liquidez');
          const savings = this.getAssetTotal(allocations, 'Poupança');
          return liquidityFunds > 30 || savings > 25;
        },
        text: 'Estratégia de liquidez - Mantendo alta reserva para oportunidades e emergências.',
        icon: '💧',
        color: '#06b6d4'
      },

      diversified: {
        priority: 6,
        condition: (allocations) => {
          const categories = ['renda-fixa', 'fundos', 'outros'];
          const activeCats = categories.filter(cat => this.getCategoryTotal(allocations, [cat]) > 10);
          return activeCats.length >= 3;
        },
        text: 'Alta diversificação - Distribuindo riscos entre múltiplas classes de ativos.',
        icon: '🌐',
        color: '#6366f1'
      },

      default: {
        priority: 999,
        condition: () => true,
        text: 'Continue alocando para descobrir sua estratégia de investimento personalizada.',
        icon: '💡',
        color: '#6b7280'
      }
    };
  }

  /**
   * Get total allocation for categories
   * @param {Object} allocations - Allocations object
   * @param {Array} categories - Category names
   * @returns {number} Total percentage
   */
  getCategoryTotal(allocations, categories) {
    let total = 0;
    for (const [asset, value] of Object.entries(allocations)) {
      const card = document.querySelector(`[data-asset="${asset}"]`);
      if (card) {
        const category = card.dataset.category;
        if (categories.includes(category)) {
          total += value;
        }
      }
    }
    return total;
  }

  /**
   * Get total allocation for asset type
   * @param {Object} allocations - Allocations object
   * @param {string} assetType - Asset type name
   * @returns {number} Total percentage
   */
  getAssetTotal(allocations, assetType) {
    let total = 0;
    for (const [asset, value] of Object.entries(allocations)) {
      const card = document.querySelector(`[data-asset="${asset}"]`);
      if (card) {
        const cardAssetType = card.querySelector('.asset-type')?.textContent?.trim() ||
                             card.querySelector('.asset-type-text')?.textContent?.trim();
        if (cardAssetType === assetType) {
          total += value;
        }
      }
    }
    return total;
  }
}

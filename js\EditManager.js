/**
 * EditManager - Handles double-click editing functionality
 * Manages direct value input with AutoNumeric integration
 */
export class EditManager {
  constructor(slider<PERSON>anager, tooltipManager, valueDisplayManager) {
    this.sliderManager = sliderManager;
    this.tooltipManager = tooltipManager;
    this.valueDisplayManager = valueDisplayManager;
    this.totalCapital = 980250.0;
    this.activeEdits = new Map(); // Track active edit sessions
  }

  /**
   * Initialize edit functionality for all cards
   */
  init() {
    this.setupDoubleClickEdit();
  }

  /**
   * Setup double click edit functionality for all cards
   */
  setupDoubleClickEdit() {
    document.querySelectorAll(".asset-card").forEach((card) => {
      this.setupCardEdit(card);
    });
  }

  /**
   * Setup edit functionality for a single card
   * @param {HTMLElement} card - Card element
   */
  setupCardEdit(card) {
    // Remove existing listeners to prevent duplicates
    const existingHandler = card._editHandler;
    if (existingHandler) {
      card.removeEventListener("dblclick", existingHandler);
    }

    // Create new handler
    const editHandler = (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.editAllocationValue(card);
    };

    card.addEventListener("dblclick", editHandler);
    card._editHandler = editHandler; // Store reference for cleanup
  }

  /**
   * Enhanced allocation value editing with improved UX
   * @param {HTMLElement} card - Card element to edit
   */
  editAllocationValue(card) {
    const allocationValueElement = card.querySelector(".allocation-value");
    const slider = card.querySelector(".allocation-slider");
    
    if (!allocationValueElement || !slider) return;

    const assetName = slider.dataset.asset;
    
    // Prevent multiple edits on the same card
    if (this.activeEdits.has(card)) return;
    
    const currentValue = allocationValueElement.textContent;
    const numericValue = this.valueDisplayManager.parseCurrency(currentValue);
    const availableBudget = this.sliderManager.calculateAvailableBudget(assetName);
    const maxAllowedValue = (this.totalCapital * availableBudget) / 100;

    // Create enhanced input element
    const input = this.createEditInput(numericValue, maxAllowedValue);
    
    // Setup edit session
    this.setupEditSession(card, allocationValueElement, input, assetName, maxAllowedValue);
  }

  /**
   * Create enhanced edit input with better styling
   * @param {number} currentValue - Current numeric value
   * @param {number} maxValue - Maximum allowed value
   * @returns {HTMLElement} Input element
   */
  createEditInput(currentValue, maxValue) {
    const input = document.createElement("input");
    input.type = "text";
    input.value = currentValue.toFixed(2);
    input.className = "edit-input";
    
    // Enhanced styling
    input.style.cssText = `
      width: 100%;
      padding: 8px 12px;
      border: 2px solid #C49725;
      border-radius: 8px;
      font-size: 20px;
      font-weight: 600;
      text-align: center;
      background: white;
      color: #1e293b;
      box-shadow: 0 4px 12px rgba(196, 151, 37, 0.2);
      transition: all 0.2s ease;
      z-index: 100;
      position: relative;
    `;

    // Add focus styles
    input.addEventListener("focus", () => {
      input.style.borderColor = "#a67b1f";
      input.style.boxShadow = "0 4px 12px rgba(196, 151, 37, 0.3), 0 0 0 3px rgba(196, 151, 37, 0.1)";
    });

    input.addEventListener("blur", () => {
      input.style.borderColor = "#C49725";
      input.style.boxShadow = "0 4px 12px rgba(196, 151, 37, 0.2)";
    });

    return input;
  }

  /**
   * Setup edit session with AutoNumeric and event handlers
   * @param {HTMLElement} card - Card element
   * @param {HTMLElement} valueElement - Value display element
   * @param {HTMLElement} input - Input element
   * @param {string} assetName - Asset name
   * @param {number} maxValue - Maximum allowed value
   */
  setupEditSession(card, valueElement, input, assetName, maxValue) {
    // Mark as active edit
    this.activeEdits.set(card, true);
    
    // Hide value element and show input
    valueElement.style.display = "none";
    valueElement.parentNode.insertBefore(input, valueElement.nextSibling);

    // Add visual feedback to card
    card.classList.add("editing");

    let autoNumeric = null;

    // Initialize AutoNumeric if available
    if (typeof AutoNumeric !== "undefined") {
      try {
        autoNumeric = new AutoNumeric(input, {
          currencySymbol: "R$ ",
          currencySymbolPlacement: AutoNumeric.options.currencySymbolPlacement.prefix,
          decimalCharacter: ",",
          digitGroupSeparator: ".",
          decimalPlaces: 2,
          minimumValue: 0,
          maximumValue: Math.min(this.totalCapital, maxValue),
          wheelStep: 1000,
          selectOnFocus: true,
          // Enhanced AutoNumeric options
          modifyValueOnWheel: true,
          watchExternalChanges: true,
          formatOnPageLoad: true,
        });

        // Add real-time validation
        input.addEventListener("input", () => {
          this.validateInputValue(input, autoNumeric, maxValue);
        });

      } catch (e) {
        console.warn("AutoNumeric initialization failed, using basic input");
        this.setupBasicValidation(input, maxValue);
      }
    } else {
      this.setupBasicValidation(input, maxValue);
    }

    // Focus and select
    input.focus();
    input.select();

    // Setup completion handlers
    const finishEdit = () => this.finishEdit(card, valueElement, input, assetName, autoNumeric);
    
    // Enhanced event handling
    input.addEventListener("blur", finishEdit);
    input.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        finishEdit();
      } else if (e.key === "Escape") {
        this.cancelEdit(card, valueElement, input, autoNumeric);
      }
    });

    // Add timeout for auto-cancel (prevent stuck edits)
    setTimeout(() => {
      if (this.activeEdits.has(card)) {
        this.cancelEdit(card, valueElement, input, autoNumeric);
      }
    }, 30000); // 30 second timeout
  }

  /**
   * Setup basic validation for input without AutoNumeric
   * @param {HTMLElement} input - Input element
   * @param {number} maxValue - Maximum allowed value
   */
  setupBasicValidation(input, maxValue) {
    input.addEventListener("input", (e) => {
      let value = e.target.value.replace(/[^\d.,]/g, "");
      value = value.replace(",", ".");
      const numericValue = parseFloat(value);
      
      if (numericValue > maxValue) {
        e.target.style.borderColor = "#ef4444";
        e.target.style.backgroundColor = "#fef2f2";
      } else {
        e.target.style.borderColor = "#C49725";
        e.target.style.backgroundColor = "white";
      }
    });
  }

  /**
   * Validate input value in real-time
   * @param {HTMLElement} input - Input element
   * @param {Object} autoNumeric - AutoNumeric instance
   * @param {number} maxValue - Maximum allowed value
   */
  validateInputValue(input, autoNumeric, maxValue) {
    try {
      const value = autoNumeric ? autoNumeric.getNumber() : parseFloat(input.value);
      
      if (value > maxValue) {
        input.style.borderColor = "#ef4444";
        input.style.backgroundColor = "#fef2f2";
        
        // Show warning tooltip
        this.showValidationWarning(input, `Valor máximo: ${this.valueDisplayManager.formatCurrency(maxValue)}`);
      } else {
        input.style.borderColor = "#C49725";
        input.style.backgroundColor = "white";
      }
    } catch (e) {
      // Handle validation errors silently
    }
  }

  /**
   * Show validation warning near input
   * @param {HTMLElement} input - Input element
   * @param {string} message - Warning message
   */
  showValidationWarning(input, message) {
    // Remove existing warning
    const existingWarning = input.parentNode.querySelector(".validation-warning");
    if (existingWarning) {
      existingWarning.remove();
    }

    // Create warning element
    const warning = document.createElement("div");
    warning.className = "validation-warning";
    warning.textContent = message;
    warning.style.cssText = `
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: #ef4444;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      text-align: center;
      z-index: 101;
      margin-top: 4px;
    `;

    input.parentNode.style.position = "relative";
    input.parentNode.appendChild(warning);

    // Auto-remove after 3 seconds
    setTimeout(() => {
      if (warning.parentNode) {
        warning.parentNode.removeChild(warning);
      }
    }, 3000);
  }

  /**
   * Finish edit session and update values
   * @param {HTMLElement} card - Card element
   * @param {HTMLElement} valueElement - Value display element
   * @param {HTMLElement} input - Input element
   * @param {string} assetName - Asset name
   * @param {Object} autoNumeric - AutoNumeric instance
   */
  finishEdit(card, valueElement, input, assetName, autoNumeric) {
    let newValue;

    try {
      if (autoNumeric) {
        newValue = autoNumeric.getNumber();
      } else {
        newValue = this.valueDisplayManager.parseCurrency(input.value);
      }
    } catch (e) {
      newValue = 0;
    }

    if (!isNaN(newValue) && newValue >= 0) {
      const percentage = (newValue / this.totalCapital) * 100;
      const availableBudget = this.sliderManager.calculateAvailableBudget(assetName);

      if (percentage > availableBudget) {
        // Handle budget exceeded
        this.handleBudgetExceeded(card, assetName, availableBudget);
      } else {
        // Update allocation
        this.updateAllocation(card, assetName, percentage, newValue);
      }
    }

    this.cleanupEdit(card, valueElement, input, autoNumeric);
  }

  /**
   * Handle budget exceeded scenario
   * @param {HTMLElement} card - Card element
   * @param {string} assetName - Asset name
   * @param {number} availableBudget - Available budget percentage
   */
  handleBudgetExceeded(card, assetName, availableBudget) {
    const slider = card.querySelector(".allocation-slider");
    this.tooltipManager.showBudgetWarning(slider);
    
    const maxPercentage = availableBudget;
    const maxValue = (this.totalCapital * maxPercentage) / 100;

    // Set to maximum allowed
    slider.value = maxPercentage;
    this.sliderManager.allocations.set(assetName, maxPercentage);

    const allocationValue = card.querySelector(".allocation-value");
    this.valueDisplayManager.updateValueDisplay(allocationValue, maxValue);
    this.valueDisplayManager.updateRemainingAmount(
      this.sliderManager.getAllocations(),
      this.totalCapital
    );
    this.valueDisplayManager.setCardActive(card, maxPercentage > 0);
    this.sliderManager.updateSliderProgress(slider, maxPercentage);

    // Show notification
    this.valueDisplayManager.showNotification(
      "Valor ajustado para o limite disponível",
      "warning"
    );
  }

  /**
   * Update allocation with new value
   * @param {HTMLElement} card - Card element
   * @param {string} assetName - Asset name
   * @param {number} percentage - New percentage
   * @param {number} value - New value
   */
  updateAllocation(card, assetName, percentage, value) {
    const slider = card.querySelector(".allocation-slider");
    slider.value = percentage;
    this.sliderManager.allocations.set(assetName, percentage);

    const allocationValue = card.querySelector(".allocation-value");
    this.valueDisplayManager.updateValueDisplay(allocationValue, value);
    this.valueDisplayManager.updateRemainingAmount(
      this.sliderManager.getAllocations(),
      this.totalCapital
    );
    this.valueDisplayManager.setCardActive(card, percentage > 0);
    this.sliderManager.updateSliderProgress(slider, percentage);

    // Dispatch change event
    this.sliderManager.dispatchAllocationChangeEvent(assetName, percentage, true);
  }

  /**
   * Cancel edit session
   * @param {HTMLElement} card - Card element
   * @param {HTMLElement} valueElement - Value display element
   * @param {HTMLElement} input - Input element
   * @param {Object} autoNumeric - AutoNumeric instance
   */
  cancelEdit(card, valueElement, input, autoNumeric) {
    this.cleanupEdit(card, valueElement, input, autoNumeric);
  }

  /**
   * Cleanup edit session
   * @param {HTMLElement} card - Card element
   * @param {HTMLElement} valueElement - Value display element
   * @param {HTMLElement} input - Input element
   * @param {Object} autoNumeric - AutoNumeric instance
   */
  cleanupEdit(card, valueElement, input, autoNumeric) {
    // Remove from active edits
    this.activeEdits.delete(card);

    // Remove visual feedback
    card.classList.remove("editing");

    // Cleanup AutoNumeric
    if (autoNumeric) {
      try {
        autoNumeric.remove();
      } catch (e) {
        // Handle cleanup errors silently
      }
    }

    // Remove input and show value element
    if (input.parentNode) {
      input.parentNode.removeChild(input);
    }
    valueElement.style.display = "block";

    // Remove any validation warnings
    const warning = card.querySelector(".validation-warning");
    if (warning && warning.parentNode) {
      warning.parentNode.removeChild(warning);
    }
  }

  /**
   * Setup edit functionality for a new card
   * @param {HTMLElement} card - New card element
   */
  setupNewCardEdit(card) {
    this.setupCardEdit(card);
  }

  /**
   * Cleanup all active edits
   */
  destroy() {
    // Cancel all active edits
    for (const card of this.activeEdits.keys()) {
      const valueElement = card.querySelector(".allocation-value");
      const input = card.querySelector(".edit-input");
      if (input && valueElement) {
        this.cancelEdit(card, valueElement, input, null);
      }
    }
    this.activeEdits.clear();
  }
}

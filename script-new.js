class ModernPortfolioManager {
  constructor() {
    this.totalCapital = 980250.0;
    this.allocations = new Map();
    this.snapPoints = [
      0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90,
      95, 100,
    ];
    this.tooltips = new Map(); // Store FloatingUI tooltip instances
    this.strategies = this.initializeStrategies();
    this.init();
  }

  init() {
    this.resetAllSliders();
    this.setupFloatingTooltips();
    this.setupSliders();
    this.setupDragAndDrop();
    this.setupDoubleClickEdit();
    this.setupSortableJS();
    this.updateDisplay();
    this.updateStrategyText();
  }

  // Reset all sliders to 0 on initialization
  resetAllSliders() {
    const sliders = document.querySelectorAll(".allocation-slider");
    sliders.forEach((slider) => {
      const assetName = slider.dataset.asset;
      slider.value = 0;
      this.allocations.set(assetName, 0);

      const card = slider.closest(".asset-card");
      const allocationValue = card.querySelector(".allocation-value");

      this.updateValueDisplay(allocationValue, 0);
      this.setCardActive(card, false);
      this.updateSliderProgress(slider, 0);
    });
  }

  // Initialize strategy definitions
  initializeStrategies() {
    return {
      conservative: {
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, [
            "renda-fixa",
          ]);
          const emergency = this.getCategoryTotal(allocations, ["fundos"]) > 20;
          return fixedIncome > 60 && emergency;
        },
        text: "Perfil conservador - Priorizando segurança e liquidez com foco em renda fixa.",
      },
      aggressive: {
        condition: (allocations) => {
          const equity = this.getCategoryTotal(allocations, ["fundos"]) > 50;
          const lowFixed =
            this.getCategoryTotal(allocations, ["renda-fixa"]) < 30;
          return equity && lowFixed;
        },
        text: "Perfil agressivo - Foco em crescimento de longo prazo com maior exposição a renda variável.",
      },
      balanced: {
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, [
            "renda-fixa",
          ]);
          const equity = this.getCategoryTotal(allocations, ["fundos"]);
          return (
            fixedIncome >= 30 &&
            fixedIncome <= 60 &&
            equity >= 20 &&
            equity <= 50
          );
        },
        text: "Perfil equilibrado - Diversificação balanceada entre renda fixa e variável.",
      },
      liquidity: {
        condition: (allocations) => {
          const liquidityFunds =
            this.getAssetTotal(allocations, "Liquidez") +
            this.getAssetTotal(allocations, "Liquidez2");
          return liquidityFunds > 30;
        },
        text: "Estratégia de liquidez - Mantendo alta reserva para oportunidades e emergências.",
      },
      alternative: {
        condition: (allocations) => {
          const alternatives = this.getCategoryTotal(allocations, ["outros"]);
          return alternatives > 25;
        },
        text: "Investimentos alternativos - Diversificando com ativos não tradicionais.",
      },
      diversified: {
        condition: (allocations) => {
          const categories = ["renda-fixa", "fundos", "outros"];
          const activeCats = categories.filter(
            (cat) => this.getCategoryTotal(allocations, [cat]) > 10
          );
          return activeCats.length >= 3;
        },
        text: "Alta diversificação - Distribuindo riscos entre múltiplas classes de ativos.",
      },
      default: {
        condition: () => true,
        text: "Continue alocando para descobrir sua estratégia de investimento.",
      },
    };
  }

  // Setup FloatingUI tooltips
  setupFloatingTooltips() {
    const sliders = document.querySelectorAll(".allocation-slider");

    sliders.forEach((slider) => {
      // Create tooltip element
      const tooltip = document.createElement("div");
      tooltip.className = "modern-tooltip";
      tooltip.innerHTML = `
        <span class="tooltip-content">0%</span>
        <div class="tooltip-arrow"></div>
      `;
      document.body.appendChild(tooltip);

      // Initialize FloatingUI
      const cleanup = FloatingUIDOM.autoUpdate(slider, tooltip, () => {
        FloatingUIDOM.computePosition(slider, tooltip, {
          placement: "top",
          middleware: [
            FloatingUIDOM.offset(12),
            FloatingUIDOM.flip(),
            FloatingUIDOM.shift({ padding: 8 }),
            FloatingUIDOM.arrow({
              element: tooltip.querySelector(".tooltip-arrow"),
            }),
          ],
        }).then(({ x, y, placement, middlewareData }) => {
          Object.assign(tooltip.style, {
            left: `${x}px`,
            top: `${y}px`,
          });

          tooltip.setAttribute("data-placement", placement);

          // Position arrow
          const arrow = tooltip.querySelector(".tooltip-arrow");
          if (middlewareData.arrow) {
            const { x: arrowX, y: arrowY } = middlewareData.arrow;
            Object.assign(arrow.style, {
              left: arrowX != null ? `${arrowX}px` : "",
              top: arrowY != null ? `${arrowY}px` : "",
            });
          }
        });
      });

      // Store tooltip and cleanup function
      this.tooltips.set(slider, { element: tooltip, cleanup });
    });
  }

  // Show tooltip with FloatingUI
  showTooltip(slider, content = null, type = "default") {
    const tooltipData = this.tooltips.get(slider);
    if (!tooltipData) return;

    const { element: tooltip } = tooltipData;
    const contentSpan = tooltip.querySelector(".tooltip-content");

    if (content) {
      contentSpan.textContent = content;
    } else {
      const value = parseFloat(slider.value);
      contentSpan.textContent = `${value.toFixed(1)}%`;
    }

    // Apply tooltip type styling
    tooltip.className = `modern-tooltip ${type}`;

    // Show tooltip
    tooltip.classList.add("show");
  }

  // Hide tooltip
  hideTooltip(slider) {
    const tooltipData = this.tooltips.get(slider);
    if (!tooltipData) return;

    const { element: tooltip } = tooltipData;
    tooltip.classList.remove("show");
  }

  // Show budget warning tooltip
  showBudgetWarning(slider) {
    this.showTooltip(slider, "Limite de orçamento excedido", "error");

    // Auto-hide after 3 seconds
    setTimeout(() => {
      this.hideTooltip(slider);
    }, 3000);
  }

  // Setup enhanced sliders
  setupSliders() {
    const sliders = document.querySelectorAll(".allocation-slider");

    sliders.forEach((slider) => {
      const assetName = slider.dataset.asset;

      // Prevent card drag when interacting with slider
      slider.addEventListener("mousedown", (e) => {
        e.stopPropagation();
        this.showTooltip(slider);
      });

      slider.addEventListener("touchstart", (e) => {
        e.stopPropagation();
        this.showTooltip(slider);
      });

      // Handle slider input with visual correlation effect
      slider.addEventListener("input", (e) => {
        this.handleSliderInput(e.target);
        this.triggerVisualCorrelation(e.target);
      });

      // Handle slider change (when user releases)
      slider.addEventListener("change", (e) => {
        this.handleSliderChange(e.target);
      });

      // Show tooltip on interaction
      slider.addEventListener("mouseenter", () => {
        this.showTooltip(slider);
        this.setCardActive(slider.closest(".asset-card"), true);
      });

      slider.addEventListener("mouseleave", () => {
        this.hideTooltip(slider);
        this.setCardActive(slider.closest(".asset-card"), false);
      });

      slider.addEventListener("focus", () => {
        this.showTooltip(slider);
      });

      slider.addEventListener("blur", () => {
        this.hideTooltip(slider);
      });

      // Prevent drag on slider container
      const sliderContainer = slider.closest(".slider-container");
      if (sliderContainer) {
        sliderContainer.addEventListener("mousedown", (e) => {
          e.stopPropagation();
        });

        sliderContainer.addEventListener("touchstart", (e) => {
          e.stopPropagation();
        });
      }
    });
  }

  // Visual correlation effect between sliders
  triggerVisualCorrelation(activeSlider) {
    const allSliders = document.querySelectorAll(".allocation-slider");
    const otherSliders = Array.from(allSliders).filter(
      (s) => s !== activeSlider
    );

    if (otherSliders.length === 0) return;

    // Pick a random slider to show visual correlation
    const randomSlider =
      otherSliders[Math.floor(Math.random() * otherSliders.length)];
    const card = randomSlider.closest(".asset-card");

    // Add a subtle visual effect without changing the actual value
    card.style.transform = "scale(1.02)";
    card.style.transition = "transform 0.2s ease";

    // Reset after a short delay
    setTimeout(() => {
      card.style.transform = "";
      card.style.transition = "";
    }, 300);
  }

  // Snap to nearest point
  snapToNearestPoint(value) {
    let closest = this.snapPoints[0];
    let minDiff = Math.abs(value - closest);

    for (let point of this.snapPoints) {
      const diff = Math.abs(value - point);
      if (diff < minDiff) {
        minDiff = diff;
        closest = point;
      }
    }

    return closest;
  }

  // Handle slider input
  handleSliderInput(slider) {
    const currentValue = parseFloat(slider.value);
    const assetName = slider.dataset.asset;

    // Check budget constraints
    const availableBudget = this.calculateAvailableBudget(assetName);
    const constrainedValue = Math.min(currentValue, availableBudget);

    // Update slider value if it exceeds budget
    if (constrainedValue !== currentValue) {
      slider.value = constrainedValue;
      this.showBudgetWarning(slider);
    } else {
      this.showTooltip(slider);
    }

    const card = slider.closest(".asset-card");

    // Update progress fill in real-time
    this.updateSliderProgress(slider, constrainedValue);

    // Real-time allocation updates
    this.allocations.set(assetName, constrainedValue);

    // Update allocation value in real-time
    const allocationValue = card.querySelector(".allocation-value");
    const value = (this.totalCapital * constrainedValue) / 100;
    this.updateValueDisplay(allocationValue, value);

    // Update remaining amount in real-time
    this.updateRemainingAmount();

    // Update card active state
    this.setCardActive(card, constrainedValue > 0);

    // Update strategy text in real-time
    this.updateStrategyText();
  }

  // Handle slider change (final value)
  handleSliderChange(slider) {
    const assetName = slider.dataset.asset;
    const rawPercentage = parseFloat(slider.value);

    // Apply budget constraints before snapping
    const availableBudget = this.calculateAvailableBudget(assetName);
    const constrainedPercentage = Math.min(rawPercentage, availableBudget);

    // Snap to nearest point
    const snappedPercentage = this.snapToNearestPoint(constrainedPercentage);
    slider.value = snappedPercentage;

    const card = slider.closest(".asset-card");

    // Update allocation
    this.allocations.set(assetName, snappedPercentage);

    // Update allocation value
    const allocationValue = card.querySelector(".allocation-value");
    const value = (this.totalCapital * snappedPercentage) / 100;
    this.updateValueDisplay(allocationValue, value);

    // Update remaining amount
    this.updateRemainingAmount();

    // Set card as active
    this.setCardActive(card, snappedPercentage > 0);

    // Update slider progress
    this.updateSliderProgress(slider, snappedPercentage);

    // Update strategy text
    this.updateStrategyText();

    // Update tooltip with final value
    this.showTooltip(slider);
  }

  // Update dynamic strategy text
  updateStrategyText() {
    const strategyElement = document.getElementById("strategy-text");
    if (!strategyElement) return;

    const currentAllocations = Object.fromEntries(this.allocations);

    // Find matching strategy
    const strategy =
      Object.values(this.strategies).find((s) =>
        s.condition(currentAllocations)
      ) || this.strategies.default;

    // Smooth text transition
    strategyElement.style.opacity = "0.5";

    setTimeout(() => {
      strategyElement.textContent = strategy.text;
      strategyElement.style.opacity = "1";
    }, 150);
  }

  // Helper methods for strategy calculation
  getCategoryTotal(allocations, categories) {
    let total = 0;
    for (const [asset, value] of Object.entries(allocations)) {
      const card = document.querySelector(`[data-asset="${asset}"]`);
      if (card) {
        const category = card.dataset.category;
        if (categories.includes(category)) {
          total += value;
        }
      }
    }
    return total;
  }

  getAssetTotal(allocations, assetType) {
    let total = 0;
    for (const [asset, value] of Object.entries(allocations)) {
      const card = document.querySelector(`[data-asset="${asset}"]`);
      if (card) {
        const cardAssetType = card
          .querySelector(".asset-type")
          .textContent.trim();
        if (cardAssetType === assetType) {
          total += value;
        }
      }
    }
    return total;
  }

  // Calculate available budget for an asset
  calculateAvailableBudget(currentAsset) {
    let totalAllocated = 0;

    for (const [asset, allocation] of this.allocations) {
      if (asset !== currentAsset) {
        totalAllocated += allocation;
      }
    }

    return Math.max(0, 100 - totalAllocated);
  }

  // Update slider visual progress
  updateSliderProgress(slider, percentage) {
    slider.style.setProperty("--progress", `${percentage}%`);
  }

  // Update currency display
  updateValueDisplay(element, value) {
    const formatted = this.formatCurrency(value);
    element.textContent = formatted;
  }

  // Format currency
  formatCurrency(value) {
    if (value === 0) return "R$0";
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  }

  // Update remaining amount
  updateRemainingAmount() {
    const totalAllocated = Array.from(this.allocations.values()).reduce(
      (sum, val) => sum + val,
      0
    );
    const remainingPercentage = Math.max(0, 100 - totalAllocated);
    const remainingValue = (this.totalCapital * remainingPercentage) / 100;

    const remainingElement = document.querySelector(".remaining-value");
    if (remainingElement) {
      this.updateValueDisplay(remainingElement, remainingValue);
    }
  }

  // Set card active state
  setCardActive(card, isActive) {
    if (isActive) {
      card.classList.add("active");
      card.classList.remove("focused");
    } else {
      card.classList.remove("active");
    }
  }

  // Update display
  updateDisplay() {
    this.updateRemainingAmount();
    this.updateStrategyText();
  }

  // Setup drag and drop functionality
  setupDragAndDrop() {
    this.setupMainGridSortable();
    this.setupSidebarChipsSortable();
  }

  setupMainGridSortable() {
    const mainGrid = document.getElementById("main-grid");

    Sortable.create(mainGrid, {
      group: {
        name: "portfolio",
        pull: true,
        put: true,
      },
      animation: 300,
      ghostClass: "sortable-ghost",
      chosenClass: "sortable-chosen",
      dragClass: "sortable-drag",
      handle: ".card-header, .asset-type, .allocation-value",

      onStart: (evt) => {
        mainGrid.classList.add("drag-over");
        this.setSliderInteractionState(false);
      },

      onEnd: (evt) => {
        mainGrid.classList.remove("drag-over");
        this.setSliderInteractionState(true);
        this.handleMainGridDrop();
      },

      onAdd: (evt) => {
        if (evt.item.classList.contains("asset-chip")) {
          this.transformChipToCard(evt.item, evt.newIndex);
        }
      },

      onRemove: (evt) => {
        if (evt.item.classList.contains("asset-card")) {
          this.transformCardToChip(evt.item, evt.from);
        }
      },
    });
  }

  setupSidebarChipsSortable() {
    const chipContainers = document.querySelectorAll(".asset-chips");

    chipContainers.forEach((container) => {
      Sortable.create(container, {
        group: {
          name: "portfolio",
          pull: "clone",
          put: true,
        },
        animation: 300,
        ghostClass: "sortable-ghost",
        chosenClass: "sortable-chosen",
        dragClass: "sortable-drag",

        onStart: (evt) => {
          container.classList.add("drag-over");
        },

        onEnd: (evt) => {
          container.classList.remove("drag-over");
        },

        onAdd: (evt) => {
          if (evt.item.classList.contains("asset-card")) {
            this.transformCardToChip(evt.item, evt.to);
          }
        },
      });
    });
  }

  transformChipToCard(chipElement, gridIndex) {
    const assetId = chipElement.dataset.assetId;
    const category = chipElement.dataset.category;
    const assetType = chipElement.dataset.assetType;

    const cardHTML = this.createCardHTML(assetId, category, assetType);
    chipElement.outerHTML = cardHTML;

    const newCard = document.querySelector(`[data-asset-id="${assetId}"]`);
    newCard.classList.add("transforming-to-card");

    setTimeout(() => {
      newCard.classList.remove("transforming-to-card");
    }, 300);

    this.setupCardSlider(newCard);
    this.updateRemainingAmount();
  }

  transformCardToChip(cardElement, targetContainer) {
    const assetId = cardElement.dataset.assetId;
    const category = cardElement.dataset.category;
    const assetTypeElement = cardElement.querySelector(".asset-type");
    const assetType = assetTypeElement
      ? assetTypeElement.textContent.trim()
      : cardElement.dataset.assetType;

    const assetName = cardElement.dataset.asset;
    if (this.allocations.has(assetName)) {
      this.allocations.set(assetName, 0);
    }

    const chipHTML = this.createChipHTML(assetId, category, assetType);
    cardElement.classList.add("transforming-to-chip");

    setTimeout(() => {
      cardElement.outerHTML = chipHTML;
      this.updateRemainingAmount();
      this.updateStrategyText();
    }, 300);
  }

  createCardHTML(assetId, category, assetType) {
    const categoryLabel = this.getCategoryLabel(category);
    const assetName = assetId.replace("-available", "").replace("-", "_");

    return `
      <div class="asset-card" data-category="${category}" data-asset="${assetName}" data-asset-id="${assetId}" data-asset-type="${assetType}">
        <div class="card-header">
          <div class="header-content">
            <div class="category-asset-line">${categoryLabel}<span class="separator">></span><span class="asset-type-text">${assetType}</span></div>
          </div>
          <i class="fas fa-edit edit-icon"></i>
        </div>
        <div class="asset-type" style="display: none;">${assetType}</div>
        <div class="allocation-value">R$0</div>
        <div class="slider-container">
          <input type="range" class="allocation-slider" min="0" max="100" value="0" data-asset="${assetName}">
        </div>
      </div>
    `;
  }

  createChipHTML(assetId, category, assetType) {
    return `
      <div class="asset-chip" draggable="true" data-asset-id="${assetId}" data-category="${category}" data-asset-type="${assetType}">
        <i class="fas fa-grip-vertical drag-handle"></i>
        <span>${assetType}</span>
      </div>
    `;
  }

  getCategoryLabel(category) {
    const labels = {
      "renda-fixa": "Renda fixa",
      fundos: "Fund. de Investimento",
      outros: "Outros",
    };
    return labels[category] || category;
  }

  setupCardSlider(card) {
    const slider = card.querySelector(".allocation-slider");
    const assetName = slider.dataset.asset;

    this.allocations.set(assetName, 0);

    // Setup FloatingUI tooltip for new slider
    this.setupSingleTooltip(slider);

    // Setup event listeners
    slider.addEventListener("mousedown", (e) => {
      e.stopPropagation();
      this.showTooltip(slider);
    });

    slider.addEventListener("touchstart", (e) => {
      e.stopPropagation();
      this.showTooltip(slider);
    });

    slider.addEventListener("input", (e) => {
      this.handleSliderInput(e.target);
      this.triggerVisualCorrelation(e.target);
    });

    slider.addEventListener("change", (e) => {
      this.handleSliderChange(e.target);
    });

    slider.addEventListener("mouseenter", () => {
      this.showTooltip(slider);
      this.setCardActive(card, true);
    });

    slider.addEventListener("mouseleave", () => {
      this.hideTooltip(slider);
      this.setCardActive(card, false);
    });

    slider.addEventListener("focus", () => {
      this.showTooltip(slider);
    });

    slider.addEventListener("blur", () => {
      this.hideTooltip(slider);
    });

    const sliderContainer = card.querySelector(".slider-container");
    if (sliderContainer) {
      sliderContainer.addEventListener("mousedown", (e) => {
        e.stopPropagation();
      });

      sliderContainer.addEventListener("touchstart", (e) => {
        e.stopPropagation();
      });
    }

    card.addEventListener("dblclick", (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.editAllocationValue(card);
    });

    const allocationValue = card.querySelector(".allocation-value");
    if (allocationValue) {
      this.updateValueDisplay(allocationValue, 0);
    }

    this.updateSliderProgress(slider, 0);
  }

  setupSingleTooltip(slider) {
    const tooltip = document.createElement("div");
    tooltip.className = "modern-tooltip";
    tooltip.innerHTML = `
      <span class="tooltip-content">0%</span>
      <div class="tooltip-arrow"></div>
    `;
    document.body.appendChild(tooltip);

    const cleanup = FloatingUIDOM.autoUpdate(slider, tooltip, () => {
      FloatingUIDOM.computePosition(slider, tooltip, {
        placement: "top",
        middleware: [
          FloatingUIDOM.offset(12),
          FloatingUIDOM.flip(),
          FloatingUIDOM.shift({ padding: 8 }),
          FloatingUIDOM.arrow({
            element: tooltip.querySelector(".tooltip-arrow"),
          }),
        ],
      }).then(({ x, y, placement, middlewareData }) => {
        Object.assign(tooltip.style, {
          left: `${x}px`,
          top: `${y}px`,
        });

        tooltip.setAttribute("data-placement", placement);

        const arrow = tooltip.querySelector(".tooltip-arrow");
        if (middlewareData.arrow) {
          const { x: arrowX, y: arrowY } = middlewareData.arrow;
          Object.assign(arrow.style, {
            left: arrowX != null ? `${arrowX}px` : "",
            top: arrowY != null ? `${arrowY}px` : "",
          });
        }
      });
    });

    this.tooltips.set(slider, { element: tooltip, cleanup });
  }

  handleMainGridDrop() {
    this.updateRemainingAmount();
    this.updateStrategyText();
  }

  setSliderInteractionState(enabled) {
    const sliders = document.querySelectorAll(".allocation-slider");
    sliders.forEach((slider) => {
      slider.style.pointerEvents = enabled ? "auto" : "none";
      const container = slider.closest(".slider-container");
      if (container) {
        container.style.pointerEvents = enabled ? "auto" : "none";
      }
    });
  }

  // Setup double click edit functionality
  setupDoubleClickEdit() {
    document.querySelectorAll(".asset-card").forEach((card) => {
      card.addEventListener("dblclick", (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.editAllocationValue(card);
      });
    });
  }

  editAllocationValue(card) {
    const allocationValueElement = card.querySelector(".allocation-value");
    const currentValue = allocationValueElement.textContent;
    const assetName = card.querySelector(".allocation-slider").dataset.asset;

    const numericValue = this.parseCurrency(currentValue);
    const availableBudget = this.calculateAvailableBudget(assetName);
    const maxAllowedValue = (this.totalCapital * availableBudget) / 100;

    const input = document.createElement("input");
    input.type = "text";
    input.value = numericValue.toFixed(2);
    input.className = "edit-input";
    input.style.cssText = `
      width: 100%;
      padding: 8px 12px;
      border: 2px solid #C49725;
      border-radius: 8px;
      font-size: 20px;
      font-weight: 600;
      text-align: center;
      background: white;
      color: #1e293b;
    `;

    allocationValueElement.style.display = "none";
    allocationValueElement.parentNode.insertBefore(
      input,
      allocationValueElement.nextSibling
    );

    let autoNumeric = null;

    if (typeof AutoNumeric !== "undefined") {
      try {
        autoNumeric = new AutoNumeric(input, {
          currencySymbol: "R$ ",
          currencySymbolPlacement:
            AutoNumeric.options.currencySymbolPlacement.prefix,
          decimalCharacter: ",",
          digitGroupSeparator: ".",
          decimalPlaces: 2,
          minimumValue: 0,
          maximumValue: Math.min(this.totalCapital, maxAllowedValue),
          wheelStep: 1000,
          selectOnFocus: true,
        });
      } catch (e) {
        console.warn("AutoNumeric initialization failed, using basic input");
      }
    }

    input.focus();
    input.select();

    const finishEdit = () => {
      let newValue;

      if (autoNumeric) {
        newValue = autoNumeric.getNumber();
      } else {
        newValue = this.parseCurrency(input.value);
      }

      if (!isNaN(newValue) && newValue >= 0) {
        const percentage = (newValue / this.totalCapital) * 100;

        if (percentage > availableBudget) {
          this.showBudgetWarning(card.querySelector(".allocation-slider"));
          const maxPercentage = availableBudget;
          const maxValue = (this.totalCapital * maxPercentage) / 100;

          const slider = card.querySelector(".allocation-slider");
          slider.value = maxPercentage;
          this.allocations.set(assetName, maxPercentage);

          this.updateValueDisplay(allocationValueElement, maxValue);
          this.updateRemainingAmount();
          this.setCardActive(card, maxPercentage > 0);
          this.updateSliderProgress(slider, maxPercentage);
          this.updateStrategyText();
        } else {
          const slider = card.querySelector(".allocation-slider");
          slider.value = percentage;
          this.allocations.set(assetName, percentage);

          this.updateValueDisplay(allocationValueElement, newValue);
          this.updateRemainingAmount();
          this.setCardActive(card, percentage > 0);
          this.updateSliderProgress(slider, percentage);
          this.updateStrategyText();
        }
      }

      if (autoNumeric) {
        autoNumeric.remove();
      }
      input.remove();
      allocationValueElement.style.display = "block";
    };

    input.addEventListener("blur", finishEdit);
    input.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        finishEdit();
      }
    });
  }

  parseCurrency(value) {
    return parseFloat(value.replace(/[^\d,.-]/g, "").replace(",", "."));
  }

  // Setup SortableJS functionality
  setupSortableJS() {
    // SortableJS is already set up in setupDragAndDrop method
    // This method exists for compatibility with the original structure
    console.log("SortableJS initialized through setupDragAndDrop");
  }

  // Cleanup tooltips on destroy
  destroy() {
    for (const [slider, { element, cleanup }] of this.tooltips) {
      cleanup();
      element.remove();
    }
    this.tooltips.clear();
  }
}

// Initialize the portfolio manager when DOM is ready
document.addEventListener("DOMContentLoaded", () => {
  window.portfolioManager = new ModernPortfolioManager();
});

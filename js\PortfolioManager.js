/**
 * PortfolioManager - Main coordinator class for the portfolio allocation interface
 * Orchestrates all managers and handles overall application state
 */
import { TooltipManager } from './TooltipManager.js';
import { SliderManager } from './SliderManager.js';
import { StrategyManager } from './StrategyManager.js';
import { ValueDisplayManager } from './ValueDisplayManager.js';
import { DragDropManager } from './DragDropManager.js';
import { EditManager } from './EditManager.js';

export class PortfolioManager {
  constructor() {
    this.totalCapital = 980250.0;
    this.isInitialized = false;
    
    // Initialize managers in dependency order
    this.valueDisplayManager = new ValueDisplayManager();
    this.tooltipManager = new TooltipManager();
    this.sliderManager = new SliderManager(this.tooltipManager, this.valueDisplayManager);
    this.strategyManager = new StrategyManager();
    this.dragDropManager = new DragDropManager(this.sliderManager, this.tooltipManager, this.valueDisplayManager);
    this.editManager = new EditManager(this.sliderManager, this.tooltipManager, this.valueDisplayManager);
    
    // Bind event handlers
    this.handleAllocationChange = this.handleAllocationChange.bind(this);
    this.handleResize = this.handleResize.bind(this);
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
  }

  /**
   * Initialize the entire portfolio management system
   */
  async init() {
    if (this.isInitialized) return;

    try {
      // Wait for range-slider-element to be ready
      await this.waitForRangeSliderElement();
      
      // Initialize all managers
      this.tooltipManager.setupFloatingTooltips();
      this.sliderManager.init();
      this.dragDropManager.init();
      this.editManager.init();
      
      // Setup global event listeners
      this.setupEventListeners();
      
      // Initial display updates
      this.updateAllDisplays();
      
      // Mark as initialized
      this.isInitialized = true;
      
      console.log("Portfolio Manager initialized successfully");
      
      // Dispatch ready event
      document.dispatchEvent(new CustomEvent("portfolioManagerReady"));
      
    } catch (error) {
      console.error("Failed to initialize Portfolio Manager:", error);
      this.showErrorMessage("Failed to initialize the application. Please refresh the page.");
    }
  }

  /**
   * Wait for range-slider-element to be available
   * @returns {Promise} Promise that resolves when ready
   */
  waitForRangeSliderElement() {
    return new Promise((resolve) => {
      if (customElements.get('range-slider')) {
        resolve();
      } else {
        customElements.whenDefined('range-slider').then(resolve);
      }
    });
  }

  /**
   * Setup global event listeners
   */
  setupEventListeners() {
    // Listen for allocation changes
    document.addEventListener("allocationChange", this.handleAllocationChange);
    
    // Listen for window resize
    window.addEventListener("resize", this.handleResize);
    
    // Listen for visibility changes (tab switching)
    document.addEventListener("visibilitychange", this.handleVisibilityChange);
    
    // Listen for keyboard shortcuts
    document.addEventListener("keydown", (e) => {
      this.handleKeyboardShortcuts(e);
    });
    
    // Listen for range-slider events specifically
    document.addEventListener("input", (e) => {
      if (e.target.matches("range-slider.allocation-slider")) {
        this.handleRangeSliderInput(e);
      }
    });
    
    document.addEventListener("change", (e) => {
      if (e.target.matches("range-slider.allocation-slider")) {
        this.handleRangeSliderChange(e);
      }
    });
  }

  /**
   * Handle range-slider input events
   * @param {Event} e - Input event
   */
  handleRangeSliderInput(e) {
    const slider = e.target;
    this.sliderManager.handleSliderInput(slider);
    this.sliderManager.triggerVisualCorrelation(slider);
  }

  /**
   * Handle range-slider change events
   * @param {Event} e - Change event
   */
  handleRangeSliderChange(e) {
    const slider = e.target;
    this.sliderManager.handleSliderChange(slider);
  }

  /**
   * Handle allocation change events
   * @param {CustomEvent} e - Allocation change event
   */
  handleAllocationChange(e) {
    const { allocations, isFinal } = e.detail;
    
    if (isFinal) {
      // Update strategy text on final changes
      this.strategyManager.updateStrategyText(allocations);
    }
    
    // Update displays
    this.updateAllDisplays();
  }

  /**
   * Handle window resize events
   */
  handleResize() {
    // Debounce resize handling
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      this.handleResizeComplete();
    }, 250);
  }

  /**
   * Handle resize completion
   */
  handleResizeComplete() {
    // Update tooltip positions
    const sliders = document.querySelectorAll(".allocation-slider");
    sliders.forEach(slider => {
      if (this.tooltipManager.isTooltipVisible(slider)) {
        this.tooltipManager.hideTooltip(slider);
        setTimeout(() => {
          this.tooltipManager.showTooltip(slider);
        }, 100);
      }
    });
  }

  /**
   * Handle visibility change (tab switching)
   */
  handleVisibilityChange() {
    if (document.hidden) {
      // Hide all tooltips when tab becomes hidden
      const sliders = document.querySelectorAll(".allocation-slider");
      sliders.forEach(slider => {
        this.tooltipManager.hideTooltip(slider);
      });
    }
  }

  /**
   * Handle keyboard shortcuts
   * @param {KeyboardEvent} e - Keyboard event
   */
  handleKeyboardShortcuts(e) {
    // Ctrl/Cmd + R: Reset all allocations
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
      e.preventDefault();
      this.resetAllAllocations();
    }
    
    // Escape: Cancel any active edits
    if (e.key === 'Escape') {
      this.cancelAllEdits();
    }
  }

  /**
   * Update all displays
   */
  updateAllDisplays() {
    const allocations = this.sliderManager.getAllocations();
    this.valueDisplayManager.updateRemainingAmount(allocations, this.totalCapital);
  }

  /**
   * Reset all allocations to zero
   */
  resetAllAllocations() {
    if (confirm("Tem certeza que deseja resetar todas as alocações?")) {
      this.sliderManager.resetAllSliders();
      this.strategyManager.updateStrategyText({});
      this.valueDisplayManager.showNotification("Todas as alocações foram resetadas", "info");
    }
  }

  /**
   * Cancel all active edits
   */
  cancelAllEdits() {
    // This will be handled by EditManager
    const editInputs = document.querySelectorAll(".edit-input");
    editInputs.forEach(input => {
      input.blur(); // This will trigger the cancel
    });
  }

  /**
   * Get current portfolio state
   * @returns {Object} Portfolio state object
   */
  getPortfolioState() {
    const allocations = Object.fromEntries(this.sliderManager.getAllocations());
    const analysis = this.strategyManager.getPortfolioAnalysis(allocations);
    
    return {
      allocations,
      totalCapital: this.totalCapital,
      analysis,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Load portfolio state
   * @param {Object} state - Portfolio state object
   */
  loadPortfolioState(state) {
    if (!state || !state.allocations) return;
    
    try {
      // Update sliders with loaded allocations
      Object.entries(state.allocations).forEach(([assetName, percentage]) => {
        const slider = document.querySelector(`[data-asset="${assetName}"]`);
        if (slider) {
          slider.value = percentage;
          this.sliderManager.allocations.set(assetName, percentage);
          
          // Update displays
          const card = slider.closest(".asset-card");
          const allocationValue = card.querySelector(".allocation-value");
          const value = (this.totalCapital * percentage) / 100;
          
          this.valueDisplayManager.updateValueDisplay(allocationValue, value, false);
          this.valueDisplayManager.setCardActive(card, percentage > 0);
          this.sliderManager.updateSliderProgress(slider, percentage);
        }
      });
      
      // Update strategy and displays
      this.strategyManager.updateStrategyText(state.allocations);
      this.updateAllDisplays();
      
      this.valueDisplayManager.showNotification("Portfolio carregado com sucesso", "success");
      
    } catch (error) {
      console.error("Failed to load portfolio state:", error);
      this.valueDisplayManager.showNotification("Erro ao carregar portfolio", "error");
    }
  }

  /**
   * Export portfolio data
   * @returns {string} JSON string of portfolio data
   */
  exportPortfolio() {
    const state = this.getPortfolioState();
    return JSON.stringify(state, null, 2);
  }

  /**
   * Import portfolio data
   * @param {string} jsonData - JSON string of portfolio data
   */
  importPortfolio(jsonData) {
    try {
      const state = JSON.parse(jsonData);
      this.loadPortfolioState(state);
    } catch (error) {
      console.error("Failed to import portfolio:", error);
      this.valueDisplayManager.showNotification("Erro ao importar portfolio", "error");
    }
  }

  /**
   * Show error message to user
   * @param {string} message - Error message
   */
  showErrorMessage(message) {
    this.valueDisplayManager.showNotification(message, "error", 5000);
  }

  /**
   * Get performance metrics
   * @returns {Object} Performance metrics
   */
  getPerformanceMetrics() {
    return {
      isInitialized: this.isInitialized,
      totalSliders: document.querySelectorAll(".allocation-slider").length,
      activeTooltips: this.tooltipManager.tooltips.size,
      activeEdits: this.editManager.activeEdits.size,
      memoryUsage: performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024),
      } : null,
    };
  }

  /**
   * Cleanup and destroy all managers
   */
  destroy() {
    // Remove event listeners
    document.removeEventListener("allocationChange", this.handleAllocationChange);
    window.removeEventListener("resize", this.handleResize);
    document.removeEventListener("visibilitychange", this.handleVisibilityChange);
    
    // Destroy all managers
    this.tooltipManager.destroy();
    this.dragDropManager.destroy();
    this.editManager.destroy();
    this.valueDisplayManager.destroy();
    
    // Clear timeouts
    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
    }
    
    this.isInitialized = false;
    
    console.log("Portfolio Manager destroyed");
  }
}

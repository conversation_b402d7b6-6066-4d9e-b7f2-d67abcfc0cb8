* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>,
    sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

.container {
  display: flex;
  min-height: 100vh;
  gap: 32px;
  padding: 32px;
  max-width: 1440px;
  margin: 0 auto;
}

/* Main Grid Layout */
.main-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 16px 12px; /* Reduced horizontal gap, maintained vertical gap */
  min-height: 640px;
}

/* Asset Cards */
.asset-card {
  background: white;
  border-radius: 16px;
  padding: 20px 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid #f1f5f9;
  opacity: 0.5; /* Default dimmed state */
}

.asset-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
  border-color: #e2e8f0;
}

.asset-card.active {
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  opacity: 1; /* Full opacity for active card */
}

.asset-card.focused {
  opacity: 1; /* Full opacity for focused card */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.header-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.category-asset-line {
  font-size: 14px;
  color: #94a3b8;
  font-weight: 400;
  letter-spacing: 0.25px;
  line-height: 1.3;
}

.category-asset-line .asset-type-text {
  font-weight: 700; /* Make asset type bold */
  color: #1e293b; /* Slightly darker for better contrast */
}

.category-asset-line .separator {
  margin: 0 6px;
  color: #cbd5e1;
}

.category-label {
  font-size: 14px;
  color: #94a3b8;
  font-weight: 400;
  letter-spacing: 0.25px;
}

.edit-icon {
  color: #64748b;
  font-size: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.asset-card:hover .edit-icon {
  opacity: 1;
}

.edit-icon:hover {
  color: #334155;
}

.asset-type {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  line-height: 1.2;
  display: none; /* Hide since we're showing it in header */
}

.allocation-value {
  font-size: 20px; /* Increased font size */
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  text-align: center;
  /* Removed background and padding for plain text styling */
  cursor: pointer; /* Indicate it's clickable */
  transition: color 0.2s ease;
}

.allocation-value:hover {
  color: #c49725; /* Highlight on hover */
}

.slider-container {
  position: relative;
  margin-top: 32px; /* Adjusted spacing */
}

.allocation-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(
    to right,
    #c49725 0%,
    /* Updated color */ #c49725 var(--progress, 0%),
    #e5e7eb var(--progress, 0%),
    #e5e7eb 100%
  );
  outline: none;
  -webkit-appearance: none;
  appearance: none; /* Added standard property */
  cursor: pointer;
  position: relative;
  transition: all 0.15s ease-out;
}

/* Slider track styling */
.allocation-slider::-webkit-slider-track {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
}

.allocation-slider::-moz-range-track {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
  border: none;
}

/* Slider thumb styling */
.allocation-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #c49725; /* Updated color */
  cursor: grab;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  border: 3px solid white;
}

.allocation-slider::-webkit-slider-thumb:hover {
  background: #a67b1f; /* Darker shade of the new color */
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(196, 151, 37, 0.3); /* Updated shadow color */
}

.allocation-slider::-webkit-slider-thumb:active {
  cursor: grabbing;
  transform: scale(0.95);
}

.allocation-slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #c49725; /* Updated color */
  cursor: grab;
  border: 3px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Snap point indicators */
.slider-container::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 8px;
  background: repeating-linear-gradient(
    to right,
    transparent 0%,
    transparent calc(20% - 2px),
    /* Made indicators more prominent */ #c49725 calc(20% - 2px),
    /* Updated color and made more visible */ #c49725 calc(20% - 1px),
    transparent calc(20% - 1px),
    transparent 20%
  );
  pointer-events: none;
  border-radius: 4px;
  transform: translateY(-50%);
  opacity: 0.4; /* Made snap points subtle but visible */
}

/* Modern Tooltip with FloatingUI */
.modern-tooltip {
  background: #1e293b;
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.2s ease, transform 0.2s ease;
  pointer-events: none;
  max-width: 200px;
  text-align: center;
  line-height: 1.3;
  transform: scale(0.9);
}

.modern-tooltip.show {
  opacity: 1;
  transform: scale(1);
}

.modern-tooltip.error {
  background: #ef4444;
  color: white;
}

.modern-tooltip.warning {
  background: #f59e0b;
  color: white;
}

/* Tooltip Arrow */
.modern-tooltip[data-placement^="top"] .tooltip-arrow {
  bottom: -4px;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #1e293b;
}

.modern-tooltip[data-placement^="bottom"] .tooltip-arrow {
  top: -4px;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid #1e293b;
}

.modern-tooltip[data-placement^="left"] .tooltip-arrow {
  right: -4px;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 4px solid #1e293b;
}

.modern-tooltip[data-placement^="right"] .tooltip-arrow {
  left: -4px;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 4px solid #1e293b;
}

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
}

/* Error tooltips arrows */
.modern-tooltip.error[data-placement^="top"] .tooltip-arrow {
  border-top-color: #ef4444;
}

.modern-tooltip.error[data-placement^="bottom"] .tooltip-arrow {
  border-bottom-color: #ef4444;
}

.modern-tooltip.error[data-placement^="left"] .tooltip-arrow {
  border-left-color: #ef4444;
}

.modern-tooltip.error[data-placement^="right"] .tooltip-arrow {
  border-right-color: #ef4444;
}

/* Warning tooltips arrows */
.modern-tooltip.warning[data-placement^="top"] .tooltip-arrow {
  border-top-color: #f59e0b;
}

.modern-tooltip.warning[data-placement^="bottom"] .tooltip-arrow {
  border-bottom-color: #f59e0b;
}

.modern-tooltip.warning[data-placement^="left"] .tooltip-arrow {
  border-left-color: #f59e0b;
}

.modern-tooltip.warning[data-placement^="right"] .tooltip-arrow {
  border-right-color: #f59e0b;
}

/* Edit input styling */
.edit-input {
  transition: all 0.2s ease;
}

.edit-input:focus {
  outline: none;
  border-color: #c49725;
  box-shadow: 0 0 0 3px rgba(196, 151, 37, 0.2);
}

/* Visual correlation effect */
.asset-card.visual-correlation {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

/* Budget warning states */
.asset-card.budget-warning {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}

.asset-card.budget-limited .allocation-slider {
  background: linear-gradient(
    to right,
    #ef4444 0%,
    #ef4444 var(--progress, 0%),
    #e5e7eb var(--progress, 0%),
    #e5e7eb 100%
  );
}

.asset-card.budget-limited .allocation-slider::-webkit-slider-thumb {
  background: #ef4444;
  border-color: #dc2626;
}

.asset-card.budget-limited .allocation-slider::-moz-range-thumb {
  background: #ef4444;
  border-color: #dc2626;
}

/* Enhanced visual feedback */
.allocation-slider:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(196, 151, 37, 0.2); /* Updated color */
}

/* Right Sidebar */
.sidebar {
  width: 340px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  height: fit-content;
  border: 1px solid #f1f5f9;
}

.portfolio-header {
  background: linear-gradient(135deg, #c49725, #a67b1f); /* Updated colors */
  color: white;
  padding: 32px 28px;
  border-radius: 24px;
  position: relative;
}

.total-label {
  font-size: 18px;
  font-weight: 400;
  opacity: 0.95;
  margin-bottom: 8px;
  text-align: left;
}

.total-value {
  font-size: 42px;
  font-weight: 300;
  letter-spacing: -1px;
  margin-bottom: 24px;
  text-align: left;
  line-height: 1.1;
}

.remaining-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.remaining-label {
  font-size: 16px;
  font-weight: 400;
  opacity: 0.9;
}

.remaining-value {
  font-size: 20px;
  font-weight: 300;
  opacity: 0.85;
}

/* Dynamic Strategy Section */
.strategy-section {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 16px 20px;
  margin-top: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.strategy-label {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.strategy-text {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.4;
  opacity: 0.95;
  min-height: 44px;
  transition: all 0.3s ease;
}

.assets-section {
  padding: 28px;
  max-height: 500px;
  overflow-y: auto;
}

.assets-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 24px;
}

.asset-category {
  margin-bottom: 28px;
}

.asset-category h4 {
  font-size: 16px;
  font-weight: 600;
  color: #475569;
  margin-bottom: 16px;
}

.asset-chips {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.asset-chip {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 15px;
  color: #475569;
  cursor: move;
  transition: all 0.3s ease;
}

.asset-chip:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.drag-handle {
  color: #94a3b8;
  font-size: 14px;
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

/* Drag and Drop Styles */
.sortable-ghost {
  opacity: 0.4;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
}

.sortable-chosen {
  transform: rotate(5deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.sortable-drag {
  transform: rotate(5deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

/* Drop zone indicators */
.main-grid.drag-over {
  background: rgba(196, 151, 37, 0.1); /* Updated color */
  border: 2px dashed #c49725; /* Updated color */
  border-radius: 12px;
}

.asset-chips.drag-over {
  background: rgba(196, 151, 37, 0.1); /* Updated color */
  border: 2px dashed #c49725; /* Updated color */
  border-radius: 8px;
  padding: 8px;
}

/* Empty grid slot styling */
.grid-slot-empty {
  background: #f9fafb;
  border: 2px dashed #e5e7eb;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 14px;
  transition: all 0.3s ease;
}

.grid-slot-empty.drag-over {
  border-color: #c49725; /* Updated color */
  background: rgba(196, 151, 37, 0.1); /* Updated color */
  color: #c49725; /* Updated color */
}

/* Chip to card transformation animation */
.transforming-to-card {
  animation: expandToCard 0.3s ease-out forwards;
}

.transforming-to-chip {
  animation: shrinkToChip 0.3s ease-out forwards;
}

@keyframes expandToCard {
  from {
    transform: scale(0.5);
    opacity: 0.5;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shrinkToChip {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.5);
    opacity: 0.5;
  }
}

/* Enhanced drag handle */
.drag-handle {
  cursor: grab;
  transition: color 0.3s ease;
}

.drag-handle:hover {
  color: #c49725; /* Updated color */
}

.drag-handle:active {
  cursor: grabbing;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
    padding: 16px;
  }

  .main-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(6, 1fr);
    min-height: auto;
  }

  .sidebar {
    width: 100%;
    order: -1;
  }

  .assets-section {
    max-height: 300px;
  }
}

/**
 * TooltipManager - Handles FloatingUI tooltip functionality
 * Manages tooltip creation, positioning, and display for sliders
 */
export class TooltipManager {
  constructor() {
    this.tooltips = new Map(); // Store FloatingUI tooltip instances
  }

  /**
   * Setup FloatingUI tooltips for all sliders
   */
  setupFloatingTooltips() {
    const sliders = document.querySelectorAll(".allocation-slider");

    sliders.forEach((slider) => {
      this.setupSingleTooltip(slider);
    });
  }

  /**
   * Setup FloatingUI tooltip for a single slider
   * @param {HTMLElement} slider - The slider element
   */
  setupSingleTooltip(slider) {
    // Create tooltip element
    const tooltip = document.createElement("div");
    tooltip.className = "modern-tooltip";
    tooltip.innerHTML = `
      <span class="tooltip-content">0%</span>
      <div class="tooltip-arrow"></div>
    `;
    document.body.appendChild(tooltip);

    // Initialize FloatingUI with enhanced configuration
    const cleanup = FloatingUIDOM.autoUpdate(slider, tooltip, () => {
      FloatingUIDOM.computePosition(slider, tooltip, {
        placement: "top",
        middleware: [
          FloatingUIDOM.offset(12),
          FloatingUIDOM.flip({
            fallbackPlacements: ["bottom", "top-start", "top-end"],
          }),
          FloatingUIDOM.shift({ 
            padding: 8,
            boundary: document.body, // Enhanced viewport boundary detection
          }),
          FloatingUIDOM.arrow({
            element: tooltip.querySelector(".tooltip-arrow"),
          }),
          // Add size middleware for better responsive behavior
          FloatingUIDOM.size({
            apply({ availableWidth, availableHeight, elements }) {
              Object.assign(elements.floating.style, {
                maxWidth: `${Math.min(200, availableWidth)}px`,
                maxHeight: `${Math.min(100, availableHeight)}px`,
              });
            },
          }),
        ],
      }).then(({ x, y, placement, middlewareData }) => {
        Object.assign(tooltip.style, {
          left: `${x}px`,
          top: `${y}px`,
        });

        tooltip.setAttribute("data-placement", placement);

        // Enhanced arrow positioning
        const arrow = tooltip.querySelector(".tooltip-arrow");
        if (middlewareData.arrow) {
          const { x: arrowX, y: arrowY } = middlewareData.arrow;
          Object.assign(arrow.style, {
            left: arrowX != null ? `${arrowX}px` : "",
            top: arrowY != null ? `${arrowY}px` : "",
          });
        }
      });
    });

    // Store tooltip and cleanup function
    this.tooltips.set(slider, { element: tooltip, cleanup });
  }

  /**
   * Show tooltip with enhanced animations and content
   * @param {HTMLElement} slider - The slider element
   * @param {string} content - Custom content (optional)
   * @param {string} type - Tooltip type (default, error, warning)
   */
  showTooltip(slider, content = null, type = "default") {
    const tooltipData = this.tooltips.get(slider);
    if (!tooltipData) return;

    const { element: tooltip } = tooltipData;
    const contentSpan = tooltip.querySelector(".tooltip-content");

    if (content) {
      contentSpan.textContent = content;
    } else {
      const value = parseFloat(slider.value);
      contentSpan.textContent = `${value.toFixed(1)}%`;
    }

    // Apply tooltip type styling with enhanced classes
    tooltip.className = `modern-tooltip ${type}`;

    // Enhanced show animation
    tooltip.classList.add("show");
    
    // Add smooth fade-in animation
    tooltip.style.transition = "opacity 0.2s ease, transform 0.2s ease";
  }

  /**
   * Hide tooltip with smooth animation
   * @param {HTMLElement} slider - The slider element
   */
  hideTooltip(slider) {
    const tooltipData = this.tooltips.get(slider);
    if (!tooltipData) return;

    const { element: tooltip } = tooltipData;
    
    // Add fade-out transition
    tooltip.style.transition = "opacity 0.15s ease, transform 0.15s ease";
    tooltip.classList.remove("show");
  }

  /**
   * Show enhanced budget warning tooltip
   * @param {HTMLElement} slider - The slider element
   * @param {string} message - Custom warning message
   */
  showBudgetWarning(slider, message = "Limite de orçamento excedido") {
    this.showTooltip(slider, message, "error");

    // Enhanced auto-hide with visual feedback
    const tooltipData = this.tooltips.get(slider);
    if (tooltipData) {
      const { element: tooltip } = tooltipData;
      
      // Add pulsing animation for attention
      tooltip.style.animation = "pulse 0.5s ease-in-out 2";
      
      setTimeout(() => {
        tooltip.style.animation = "";
        this.hideTooltip(slider);
      }, 3000);
    }
  }

  /**
   * Show success tooltip for valid allocations
   * @param {HTMLElement} slider - The slider element
   * @param {string} message - Success message
   */
  showSuccessTooltip(slider, message = "Alocação válida") {
    this.showTooltip(slider, message, "success");
    
    setTimeout(() => {
      this.hideTooltip(slider);
    }, 2000);
  }

  /**
   * Update tooltip content in real-time during slider interaction
   * @param {HTMLElement} slider - The slider element
   * @param {number} value - Current slider value
   * @param {number} totalCapital - Total capital amount
   */
  updateTooltipContent(slider, value, totalCapital) {
    const tooltipData = this.tooltips.get(slider);
    if (!tooltipData) return;

    const { element: tooltip } = tooltipData;
    const contentSpan = tooltip.querySelector(".tooltip-content");
    
    if (tooltip.classList.contains("show")) {
      const percentage = value.toFixed(1);
      const currency = this.formatCurrency((totalCapital * value) / 100);
      contentSpan.innerHTML = `${percentage}%<br><small>${currency}</small>`;
    }
  }

  /**
   * Format currency for tooltip display
   * @param {number} value - Currency value
   * @returns {string} Formatted currency string
   */
  formatCurrency(value) {
    if (value === 0) return "R$0";
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  }

  /**
   * Enhanced tooltip positioning for better UX
   * @param {HTMLElement} slider - The slider element
   * @param {string} preferredPlacement - Preferred tooltip placement
   */
  updateTooltipPlacement(slider, preferredPlacement = "top") {
    const tooltipData = this.tooltips.get(slider);
    if (!tooltipData) return;

    const { cleanup } = tooltipData;
    
    // Update the FloatingUI configuration with new placement
    cleanup(); // Clean up existing auto-update
    this.setupSingleTooltip(slider); // Re-setup with new configuration
  }

  /**
   * Cleanup all tooltips
   */
  destroy() {
    for (const [slider, { element, cleanup }] of this.tooltips) {
      cleanup();
      element.remove();
    }
    this.tooltips.clear();
  }

  /**
   * Get tooltip element for a slider (for external styling)
   * @param {HTMLElement} slider - The slider element
   * @returns {HTMLElement|null} Tooltip element
   */
  getTooltipElement(slider) {
    const tooltipData = this.tooltips.get(slider);
    return tooltipData ? tooltipData.element : null;
  }

  /**
   * Check if tooltip is currently visible
   * @param {HTMLElement} slider - The slider element
   * @returns {boolean} True if tooltip is visible
   */
  isTooltipVisible(slider) {
    const tooltipData = this.tooltips.get(slider);
    return tooltipData ? tooltipData.element.classList.contains("show") : false;
  }
}

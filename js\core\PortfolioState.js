/**
 * PortfolioState - Centralized state management for portfolio allocations
 * Manages all allocation data and provides reactive updates
 */
import { eventBus } from './EventBus.js';

export class PortfolioState {
  constructor() {
    this.totalCapital = 980250.0;
    this.allocations = new Map(); // assetName -> percentage
    this.snapPoints = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100];
    
    // Initialize all assets to 0
    this.initializeAssets();
  }

  /**
   * Initialize all assets from the DOM
   */
  initializeAssets() {
    const sliders = document.querySelectorAll('range-slider.allocation-slider');
    sliders.forEach(slider => {
      const assetName = slider.dataset.asset;
      if (assetName) {
        this.allocations.set(assetName, 0);
      }
    });
  }

  /**
   * Set allocation for an asset
   * @param {string} assetName - Asset name
   * @param {number} percentage - Allocation percentage
   * @param {boolean} skipValidation - Skip budget validation
   */
  setAllocation(assetName, percentage, skipValidation = false) {
    const oldValue = this.allocations.get(assetName) || 0;
    
    if (!skipValidation) {
      // Validate budget constraints
      const availableBudget = this.getAvailableBudget(assetName);
      percentage = Math.min(percentage, availableBudget);
    }
    
    // Snap to nearest point
    percentage = this.snapToNearestPoint(percentage);
    
    // Update allocation
    this.allocations.set(assetName, percentage);
    
    // Emit change event
    eventBus.emit('allocation:changed', {
      assetName,
      oldValue,
      newValue: percentage,
      allocations: this.getAllocations(),
      totalAllocated: this.getTotalAllocated(),
      remainingBudget: this.getRemainingBudget()
    });
  }

  /**
   * Get allocation for an asset
   * @param {string} assetName - Asset name
   * @returns {number} Allocation percentage
   */
  getAllocation(assetName) {
    return this.allocations.get(assetName) || 0;
  }

  /**
   * Get all allocations
   * @returns {Object} Object with all allocations
   */
  getAllocations() {
    return Object.fromEntries(this.allocations);
  }

  /**
   * Get total allocated percentage
   * @returns {number} Total allocated percentage
   */
  getTotalAllocated() {
    return Array.from(this.allocations.values()).reduce((sum, val) => sum + val, 0);
  }

  /**
   * Get remaining budget percentage
   * @returns {number} Remaining budget percentage
   */
  getRemainingBudget() {
    return Math.max(0, 100 - this.getTotalAllocated());
  }

  /**
   * Get available budget for a specific asset
   * @param {string} assetName - Asset name
   * @returns {number} Available budget percentage
   */
  getAvailableBudget(assetName) {
    let totalOthers = 0;
    for (const [asset, allocation] of this.allocations) {
      if (asset !== assetName) {
        totalOthers += allocation;
      }
    }
    return Math.max(0, 100 - totalOthers);
  }

  /**
   * Snap value to nearest snap point
   * @param {number} value - Value to snap
   * @returns {number} Snapped value
   */
  snapToNearestPoint(value) {
    let closest = this.snapPoints[0];
    let minDiff = Math.abs(value - closest);

    for (let point of this.snapPoints) {
      const diff = Math.abs(value - point);
      if (diff < minDiff) {
        minDiff = diff;
        closest = point;
      }
    }

    return closest;
  }

  /**
   * Reset all allocations to zero
   */
  resetAll() {
    const oldAllocations = this.getAllocations();
    
    for (const assetName of this.allocations.keys()) {
      this.allocations.set(assetName, 0);
    }
    
    eventBus.emit('allocation:reset', {
      oldAllocations,
      newAllocations: this.getAllocations()
    });
  }

  /**
   * Get currency value for a percentage
   * @param {number} percentage - Percentage value
   * @returns {number} Currency value
   */
  getCurrencyValue(percentage) {
    return (this.totalCapital * percentage) / 100;
  }

  /**
   * Get percentage from currency value
   * @param {number} currencyValue - Currency value
   * @returns {number} Percentage value
   */
  getPercentageFromCurrency(currencyValue) {
    return (currencyValue / this.totalCapital) * 100;
  }

  /**
   * Format currency value
   * @param {number} value - Currency value
   * @returns {string} Formatted currency string
   */
  formatCurrency(value) {
    if (value === 0) return "R$0";
    
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  }

  /**
   * Get portfolio summary
   * @returns {Object} Portfolio summary
   */
  getSummary() {
    return {
      totalCapital: this.totalCapital,
      totalAllocated: this.getTotalAllocated(),
      remainingBudget: this.getRemainingBudget(),
      allocations: this.getAllocations(),
      currencyAllocations: Object.fromEntries(
        Array.from(this.allocations.entries()).map(([asset, percentage]) => [
          asset,
          this.getCurrencyValue(percentage)
        ])
      )
    };
  }
}

// Create global state instance
export const portfolioState = new PortfolioState();

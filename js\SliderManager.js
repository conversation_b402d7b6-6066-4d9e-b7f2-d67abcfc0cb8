/**
 * SliderManager - Handles range-slider-element functionality
 * Manages slider interactions, snap points, and visual correlation effects
 */
export class SliderManager {
  constructor(tooltipManager, valueDisplayManager) {
    this.tooltipManager = tooltipManager;
    this.valueDisplayManager = valueDisplayManager;
    this.allocations = new Map();
    this.totalCapital = 980250.0;
    
    // Enhanced snap points with more granular control
    this.snapPoints = [
      0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 
      55, 60, 65, 70, 75, 80, 85, 90, 95, 100,
    ];
    
    // Visual correlation settings
    this.correlationEffects = {
      enabled: true,
      intensity: 0.02, // Scale factor for visual correlation
      duration: 300, // Animation duration in ms
    };
  }

  /**
   * Initialize all sliders with range-slider-element
   */
  init() {
    this.resetAllSliders();
    this.setupSliders();
  }

  /**
   * Reset all sliders to 0 on initialization
   */
  resetAllSliders() {
    const sliders = document.querySelectorAll(".allocation-slider");
    sliders.forEach((slider) => {
      const assetName = slider.dataset.asset;
      slider.value = 0;
      this.allocations.set(assetName, 0);

      const card = slider.closest(".asset-card");
      const allocationValue = card.querySelector(".allocation-value");

      this.valueDisplayManager.updateValueDisplay(allocationValue, 0);
      this.valueDisplayManager.setCardActive(card, false);
      this.updateSliderProgress(slider, 0);
    });
  }

  /**
   * Setup enhanced sliders with range-slider-element
   */
  setupSliders() {
    const sliders = document.querySelectorAll(".allocation-slider");

    sliders.forEach((slider) => {
      this.setupSingleSlider(slider);
    });
  }

  /**
   * Setup a single slider with all event listeners
   * @param {HTMLElement} slider - The range-slider element
   */
  setupSingleSlider(slider) {
    const assetName = slider.dataset.asset;
    this.allocations.set(assetName, 0);

    // Prevent card drag when interacting with slider
    slider.addEventListener("mousedown", (e) => {
      e.stopPropagation();
      this.tooltipManager.showTooltip(slider);
    });

    slider.addEventListener("touchstart", (e) => {
      e.stopPropagation();
      this.tooltipManager.showTooltip(slider);
    });

    // Enhanced input handling with real-time updates
    slider.addEventListener("input", (e) => {
      this.handleSliderInput(e.target);
      this.triggerVisualCorrelation(e.target);
    });

    // Handle slider change (when user releases)
    slider.addEventListener("change", (e) => {
      this.handleSliderChange(e.target);
    });

    // Enhanced hover interactions
    slider.addEventListener("mouseenter", () => {
      this.tooltipManager.showTooltip(slider);
      const card = slider.closest(".asset-card");
      this.valueDisplayManager.setCardActive(card, true);
    });

    slider.addEventListener("mouseleave", () => {
      this.tooltipManager.hideTooltip(slider);
      const card = slider.closest(".asset-card");
      const currentValue = parseFloat(slider.value);
      this.valueDisplayManager.setCardActive(card, currentValue > 0);
    });

    // Keyboard accessibility
    slider.addEventListener("focus", () => {
      this.tooltipManager.showTooltip(slider);
    });

    slider.addEventListener("blur", () => {
      this.tooltipManager.hideTooltip(slider);
    });

    // Enhanced keyboard navigation
    slider.addEventListener("keydown", (e) => {
      this.handleKeyboardNavigation(e, slider);
    });

    // Prevent drag on slider container
    const sliderContainer = slider.closest(".slider-container");
    if (sliderContainer) {
      sliderContainer.addEventListener("mousedown", (e) => {
        e.stopPropagation();
      });

      sliderContainer.addEventListener("touchstart", (e) => {
        e.stopPropagation();
      });
    }
  }

  /**
   * Enhanced keyboard navigation for sliders
   * @param {KeyboardEvent} e - Keyboard event
   * @param {HTMLElement} slider - The slider element
   */
  handleKeyboardNavigation(e, slider) {
    const currentValue = parseFloat(slider.value);
    let newValue = currentValue;

    switch (e.key) {
      case "ArrowLeft":
      case "ArrowDown":
        newValue = Math.max(0, currentValue - 5);
        break;
      case "ArrowRight":
      case "ArrowUp":
        newValue = Math.min(100, currentValue + 5);
        break;
      case "Home":
        newValue = 0;
        break;
      case "End":
        newValue = 100;
        break;
      case "PageDown":
        newValue = Math.max(0, currentValue - 10);
        break;
      case "PageUp":
        newValue = Math.min(100, currentValue + 10);
        break;
      default:
        return; // Don't prevent default for other keys
    }

    e.preventDefault();
    slider.value = newValue;
    this.handleSliderInput(slider);
    this.handleSliderChange(slider);
  }

  /**
   * Enhanced visual correlation effect between sliders
   * @param {HTMLElement} activeSlider - The currently active slider
   */
  triggerVisualCorrelation(activeSlider) {
    if (!this.correlationEffects.enabled) return;

    const allSliders = document.querySelectorAll(".allocation-slider");
    const otherSliders = Array.from(allSliders).filter(
      (s) => s !== activeSlider
    );

    if (otherSliders.length === 0) return;

    // Enhanced correlation logic - pick multiple sliders for more realistic effect
    const numCorrelated = Math.min(2, otherSliders.length);
    const correlatedSliders = this.getRandomSliders(otherSliders, numCorrelated);

    correlatedSliders.forEach((slider, index) => {
      const card = slider.closest(".asset-card");
      const delay = index * 50; // Stagger the animations

      setTimeout(() => {
        // Enhanced visual effects
        card.style.transform = "scale(1.02) translateY(-2px)";
        card.style.transition = "transform 0.2s ease, box-shadow 0.2s ease";
        card.style.boxShadow = "0 8px 25px rgba(196, 151, 37, 0.15)";

        // Add subtle glow effect
        card.style.borderColor = "rgba(196, 151, 37, 0.3)";

        // Reset after animation
        setTimeout(() => {
          card.style.transform = "";
          card.style.transition = "";
          card.style.boxShadow = "";
          card.style.borderColor = "";
        }, this.correlationEffects.duration);
      }, delay);
    });
  }

  /**
   * Get random sliders for correlation effect
   * @param {Array} sliders - Array of slider elements
   * @param {number} count - Number of sliders to select
   * @returns {Array} Selected sliders
   */
  getRandomSliders(sliders, count) {
    const shuffled = [...sliders].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  /**
   * Snap to nearest point with enhanced logic
   * @param {number} value - Current value
   * @returns {number} Snapped value
   */
  snapToNearestPoint(value) {
    let closest = this.snapPoints[0];
    let minDiff = Math.abs(value - closest);

    for (let point of this.snapPoints) {
      const diff = Math.abs(value - point);
      if (diff < minDiff) {
        minDiff = diff;
        closest = point;
      }
    }

    return closest;
  }

  /**
   * Handle slider input with enhanced real-time updates
   * @param {HTMLElement} slider - The slider element
   */
  handleSliderInput(slider) {
    const currentValue = parseFloat(slider.value);
    const assetName = slider.dataset.asset;

    // Check budget constraints
    const availableBudget = this.calculateAvailableBudget(assetName);
    const constrainedValue = Math.min(currentValue, availableBudget);

    // Update slider value if it exceeds budget
    if (constrainedValue !== currentValue) {
      slider.value = constrainedValue;
      this.tooltipManager.showBudgetWarning(slider);
    } else {
      this.tooltipManager.updateTooltipContent(slider, constrainedValue, this.totalCapital);
    }

    const card = slider.closest(".asset-card");

    // Update progress fill in real-time
    this.updateSliderProgress(slider, constrainedValue);

    // Real-time allocation updates
    this.allocations.set(assetName, constrainedValue);

    // Update allocation value in real-time
    const allocationValue = card.querySelector(".allocation-value");
    const value = (this.totalCapital * constrainedValue) / 100;
    this.valueDisplayManager.updateValueDisplay(allocationValue, value);

    // Update remaining amount in real-time
    this.valueDisplayManager.updateRemainingAmount(this.allocations, this.totalCapital);

    // Update card active state
    this.valueDisplayManager.setCardActive(card, constrainedValue > 0);

    // Dispatch custom event for strategy updates
    this.dispatchAllocationChangeEvent(assetName, constrainedValue);
  }

  /**
   * Handle slider change (final value) with snapping
   * @param {HTMLElement} slider - The slider element
   */
  handleSliderChange(slider) {
    const assetName = slider.dataset.asset;
    const rawPercentage = parseFloat(slider.value);

    // Apply budget constraints before snapping
    const availableBudget = this.calculateAvailableBudget(assetName);
    const constrainedPercentage = Math.min(rawPercentage, availableBudget);

    // Snap to nearest point
    const snappedPercentage = this.snapToNearestPoint(constrainedPercentage);
    slider.value = snappedPercentage;

    const card = slider.closest(".asset-card");

    // Update allocation
    this.allocations.set(assetName, snappedPercentage);

    // Update allocation value
    const allocationValue = card.querySelector(".allocation-value");
    const value = (this.totalCapital * snappedPercentage) / 100;
    this.valueDisplayManager.updateValueDisplay(allocationValue, value);

    // Update remaining amount
    this.valueDisplayManager.updateRemainingAmount(this.allocations, this.totalCapital);

    // Set card as active
    this.valueDisplayManager.setCardActive(card, snappedPercentage > 0);

    // Update slider progress
    this.updateSliderProgress(slider, snappedPercentage);

    // Update tooltip with final value
    this.tooltipManager.showTooltip(slider);

    // Dispatch final change event
    this.dispatchAllocationChangeEvent(assetName, snappedPercentage, true);
  }

  /**
   * Calculate available budget for an asset
   * @param {string} currentAsset - Current asset name
   * @returns {number} Available budget percentage
   */
  calculateAvailableBudget(currentAsset) {
    let totalAllocated = 0;

    for (const [asset, allocation] of this.allocations) {
      if (asset !== currentAsset) {
        totalAllocated += allocation;
      }
    }

    return Math.max(0, 100 - totalAllocated);
  }

  /**
   * Update slider visual progress
   * @param {HTMLElement} slider - The slider element
   * @param {number} percentage - Progress percentage
   */
  updateSliderProgress(slider, percentage) {
    slider.style.setProperty("--progress", `${percentage}%`);
  }

  /**
   * Dispatch custom allocation change event
   * @param {string} assetName - Asset name
   * @param {number} value - New value
   * @param {boolean} isFinal - Whether this is a final change
   */
  dispatchAllocationChangeEvent(assetName, value, isFinal = false) {
    const event = new CustomEvent("allocationChange", {
      detail: {
        assetName,
        value,
        isFinal,
        allocations: Object.fromEntries(this.allocations),
      },
    });
    document.dispatchEvent(event);
  }

  /**
   * Get current allocations
   * @returns {Map} Current allocations map
   */
  getAllocations() {
    return new Map(this.allocations);
  }

  /**
   * Set slider interaction state (for drag and drop)
   * @param {boolean} enabled - Whether sliders should be interactive
   */
  setSliderInteractionState(enabled) {
    const sliders = document.querySelectorAll(".allocation-slider");
    sliders.forEach((slider) => {
      slider.style.pointerEvents = enabled ? "auto" : "none";
      const container = slider.closest(".slider-container");
      if (container) {
        container.style.pointerEvents = enabled ? "auto" : "none";
      }
    });
  }
}

/**
 * Main Application - Clean, modern portfolio allocation interface
 * Built specifically for range-slider-element and FloatingUI
 */
import { eventBus } from './core/EventBus.js';
import { portfolioState } from './core/PortfolioState.js';
import { SliderComponent } from './components/SliderComponent.js';
import { DisplayManager } from './components/DisplayManager.js';
import { CorrelationManager } from './components/CorrelationManager.js';

class PortfolioApp {
  constructor() {
    this.sliders = new Map();
    this.displayManager = null;
    this.correlationManager = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the application
   */
  async init() {
    try {
      console.log('Initializing Portfolio App...');
      
      // Show loading indicator
      this.showLoadingIndicator();
      
      // Wait for range-slider-element to be ready
      await this.waitForCustomElements();
      
      // Initialize components
      this.initializeSliders();
      this.displayManager = new DisplayManager();
      this.correlationManager = new CorrelationManager();
      
      // Setup global event listeners
      this.setupGlobalEventListeners();
      
      // Initial state update
      this.updateAllDisplays();
      
      // Hide loading indicator
      this.hideLoadingIndicator();
      
      this.isInitialized = true;
      console.log('Portfolio App initialized successfully');
      
      // Show welcome message
      this.showWelcomeMessage();
      
    } catch (error) {
      console.error('Failed to initialize Portfolio App:', error);
      this.showErrorMessage('Falha ao inicializar a aplicação. Recarregue a página.');
    }
  }

  /**
   * Wait for custom elements to be defined
   */
  async waitForCustomElements() {
    if (customElements.get('range-slider')) {
      return Promise.resolve();
    }
    
    return customElements.whenDefined('range-slider');
  }

  /**
   * Initialize all slider components
   */
  initializeSliders() {
    const sliderElements = document.querySelectorAll('range-slider.allocation-slider');
    
    sliderElements.forEach(sliderElement => {
      const assetName = sliderElement.dataset.asset;
      if (assetName) {
        const sliderComponent = new SliderComponent(sliderElement);
        this.sliders.set(assetName, sliderComponent);
      }
    });
    
    console.log(`Initialized ${this.sliders.size} sliders`);
  }

  /**
   * Setup global event listeners
   */
  setupGlobalEventListeners() {
    // Keyboard shortcuts
    document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    
    // Window events
    window.addEventListener('resize', this.handleResize.bind(this));
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    
    // Visibility change
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    
    // Error handling
    window.addEventListener('error', this.handleError.bind(this));
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));
  }

  /**
   * Handle keyboard shortcuts
   * @param {KeyboardEvent} e - Keyboard event
   */
  handleKeyboardShortcuts(e) {
    // Ctrl/Cmd + R: Reset all allocations
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
      e.preventDefault();
      this.resetAllAllocations();
    }
    
    // Escape: Hide all tooltips
    if (e.key === 'Escape') {
      this.hideAllTooltips();
    }
  }

  /**
   * Handle window resize
   */
  handleResize() {
    // Debounce resize handling
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      this.handleResizeComplete();
    }, 250);
  }

  /**
   * Handle resize completion
   */
  handleResizeComplete() {
    // Update tooltip positions by hiding and showing them
    this.sliders.forEach(slider => {
      if (slider.tooltip && slider.tooltip.classList.contains('show')) {
        slider.hideTooltip();
        setTimeout(() => slider.showTooltip(), 100);
      }
    });
  }

  /**
   * Handle before unload
   */
  handleBeforeUnload() {
    this.cleanup();
  }

  /**
   * Handle visibility change
   */
  handleVisibilityChange() {
    if (document.hidden) {
      this.hideAllTooltips();
    }
  }

  /**
   * Handle JavaScript errors
   * @param {ErrorEvent} e - Error event
   */
  handleError(e) {
    console.error('JavaScript error:', e.error);
    this.showErrorMessage('Erro detectado. A aplicação pode não funcionar corretamente.');
  }

  /**
   * Handle unhandled promise rejections
   * @param {PromiseRejectionEvent} e - Promise rejection event
   */
  handleUnhandledRejection(e) {
    console.error('Unhandled promise rejection:', e.reason);
    this.showErrorMessage('Erro inesperado detectado.');
  }

  /**
   * Update all displays
   */
  updateAllDisplays() {
    if (this.displayManager) {
      this.displayManager.updateAllDisplays();
    }
  }

  /**
   * Reset all allocations
   */
  resetAllAllocations() {
    if (confirm('Tem certeza que deseja resetar todas as alocações?')) {
      portfolioState.resetAll();
      this.showNotification('Todas as alocações foram resetadas', 'info');
    }
  }

  /**
   * Hide all tooltips
   */
  hideAllTooltips() {
    this.sliders.forEach(slider => {
      slider.hideTooltip();
    });
  }

  /**
   * Show loading indicator
   */
  showLoadingIndicator() {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'app-loading';
    loadingDiv.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.95);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(2px);
      ">
        <div style="text-align: center; color: #1e293b;">
          <div style="
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #c49725;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
          "></div>
          <div style="font-size: 16px; font-weight: 500; color: #64748b;">
            Carregando interface...
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(loadingDiv);
  }

  /**
   * Hide loading indicator
   */
  hideLoadingIndicator() {
    const loadingDiv = document.getElementById('app-loading');
    if (loadingDiv) {
      loadingDiv.style.opacity = '0';
      loadingDiv.style.transition = 'opacity 0.3s ease';
      
      setTimeout(() => {
        if (loadingDiv.parentNode) {
          loadingDiv.parentNode.removeChild(loadingDiv);
        }
      }, 300);
    }
  }

  /**
   * Show welcome message
   */
  showWelcomeMessage() {
    if (!localStorage.getItem('portfolio-welcome-shown')) {
      setTimeout(() => {
        this.showNotification(
          'Bem-vindo! Use os sliders para alocar seu patrimônio.',
          'info',
          5000
        );
        localStorage.setItem('portfolio-welcome-shown', 'true');
      }, 1000);
    }
  }

  /**
   * Show notification
   * @param {string} message - Notification message
   * @param {string} type - Notification type
   * @param {number} duration - Duration in milliseconds
   */
  showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 10);
    
    // Animate out and remove
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, duration);
  }

  /**
   * Show error message
   * @param {string} message - Error message
   */
  showErrorMessage(message) {
    this.showNotification(message, 'error', 5000);
  }

  /**
   * Get application state
   * @returns {Object} Application state
   */
  getState() {
    return {
      isInitialized: this.isInitialized,
      sliderCount: this.sliders.size,
      portfolioSummary: portfolioState.getSummary(),
      correlationSettings: this.correlationManager?.getSettings(),
    };
  }

  /**
   * Cleanup application
   */
  cleanup() {
    // Cleanup sliders
    this.sliders.forEach(slider => {
      slider.destroy();
    });
    this.sliders.clear();
    
    // Clear event bus
    eventBus.clear();
    
    // Clear timeouts
    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
    }
    
    console.log('Portfolio App cleaned up');
  }
}

// Initialize app when DOM is ready
let app = null;

function initializeApp() {
  app = new PortfolioApp();
  app.init();
  
  // Make app available globally for debugging
  if (window.location.hostname === 'localhost' || window.location.search.includes('debug=true')) {
    window.portfolioApp = app;
    window.portfolioState = portfolioState;
    window.eventBus = eventBus;
  }
}

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  initializeApp();
}

export { app };

/**
 * ValueDisplayManager - Handles currency formatting and display updates
 * Manages card states, remaining amounts, and value presentations
 */
export class ValueDisplayManager {
  constructor() {
    this.totalCapital = 980250.0;
    this.animationQueue = new Map(); // For managing multiple simultaneous animations
  }

  /**
   * Update currency display with enhanced formatting and animations
   * @param {HTMLElement} element - Element to update
   * @param {number} value - Currency value
   * @param {boolean} animate - Whether to animate the change
   */
  updateValueDisplay(element, value, animate = true) {
    if (!element) return;

    const formatted = this.formatCurrency(value);
    const currentText = element.textContent;

    if (animate && currentText !== formatted) {
      this.animateValueChange(element, currentText, formatted);
    } else {
      element.textContent = formatted;
    }

    // Add visual feedback for significant changes
    if (value > 0) {
      element.classList.add("has-value");
    } else {
      element.classList.remove("has-value");
    }
  }

  /**
   * Animate value changes with smooth transitions
   * @param {HTMLElement} element - Element to animate
   * @param {string} fromText - Starting text
   * @param {string} toText - Ending text
   */
  animateValueChange(element, fromText, toText) {
    // Cancel any existing animation for this element
    if (this.animationQueue.has(element)) {
      clearTimeout(this.animationQueue.get(element));
    }

    // Add transition class
    element.classList.add("value-changing");
    
    // Animate the change
    element.style.transform = "scale(1.05)";
    element.style.transition = "transform 0.15s ease";

    const timeout = setTimeout(() => {
      element.textContent = toText;
      element.style.transform = "scale(1)";
      
      setTimeout(() => {
        element.classList.remove("value-changing");
        element.style.transition = "";
        this.animationQueue.delete(element);
      }, 150);
    }, 75);

    this.animationQueue.set(element, timeout);
  }

  /**
   * Enhanced currency formatting with locale support
   * @param {number} value - Currency value
   * @returns {string} Formatted currency string
   */
  formatCurrency(value) {
    if (value === 0) return "R$0";
    
    // Enhanced formatting for different value ranges
    if (value >= 1000000) {
      // For millions, show with M suffix for better readability
      const millions = value / 1000000;
      if (millions >= 10) {
        return `R$${millions.toFixed(1)}M`;
      } else {
        return new Intl.NumberFormat("pt-BR", {
          style: "currency",
          currency: "BRL",
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(value);
      }
    } else if (value >= 1000) {
      // For thousands, use standard formatting
      return new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: "BRL",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(value);
    } else {
      // For smaller values, show with decimals if needed
      return new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: "BRL",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }).format(value);
    }
  }

  /**
   * Update remaining amount with enhanced visual feedback
   * @param {Map} allocations - Current allocations map
   * @param {number} totalCapital - Total capital amount
   */
  updateRemainingAmount(allocations, totalCapital = this.totalCapital) {
    const totalAllocated = Array.from(allocations.values()).reduce(
      (sum, val) => sum + val,
      0
    );
    const remainingPercentage = Math.max(0, 100 - totalAllocated);
    const remainingValue = (totalCapital * remainingPercentage) / 100;

    const remainingElement = document.querySelector(".remaining-value");
    if (remainingElement) {
      this.updateValueDisplay(remainingElement, remainingValue);
      
      // Add visual feedback based on remaining amount
      const parentSection = remainingElement.closest(".remaining-section");
      if (parentSection) {
        parentSection.classList.remove("low-remaining", "no-remaining", "high-remaining");
        
        if (remainingPercentage === 0) {
          parentSection.classList.add("no-remaining");
        } else if (remainingPercentage < 10) {
          parentSection.classList.add("low-remaining");
        } else if (remainingPercentage > 50) {
          parentSection.classList.add("high-remaining");
        }
      }
    }

    // Update progress indicator if exists
    this.updateAllocationProgress(totalAllocated);
  }

  /**
   * Update overall allocation progress indicator
   * @param {number} totalAllocated - Total allocated percentage
   */
  updateAllocationProgress(totalAllocated) {
    const progressElement = document.querySelector(".allocation-progress");
    if (progressElement) {
      progressElement.style.setProperty("--progress", `${totalAllocated}%`);
      
      // Add completion classes
      progressElement.classList.remove("quarter", "half", "three-quarters", "complete");
      
      if (totalAllocated >= 100) {
        progressElement.classList.add("complete");
      } else if (totalAllocated >= 75) {
        progressElement.classList.add("three-quarters");
      } else if (totalAllocated >= 50) {
        progressElement.classList.add("half");
      } else if (totalAllocated >= 25) {
        progressElement.classList.add("quarter");
      }
    }
  }

  /**
   * Enhanced card active state management
   * @param {HTMLElement} card - Card element
   * @param {boolean} isActive - Whether card should be active
   * @param {string} reason - Reason for state change (hover, allocation, focus)
   */
  setCardActive(card, isActive, reason = "allocation") {
    if (!card) return;

    // Remove all state classes
    card.classList.remove("active", "focused", "hover", "budget-warning");

    if (isActive) {
      switch (reason) {
        case "hover":
          card.classList.add("focused", "hover");
          break;
        case "focus":
          card.classList.add("focused");
          break;
        case "budget-warning":
          card.classList.add("budget-warning");
          break;
        default:
          card.classList.add("active");
      }
    }

    // Add smooth transition
    if (!card.style.transition) {
      card.style.transition = "all 0.2s ease";
    }
  }

  /**
   * Set budget warning state for a card
   * @param {HTMLElement} card - Card element
   * @param {boolean} hasWarning - Whether card has budget warning
   */
  setBudgetWarningState(card, hasWarning) {
    if (!card) return;

    if (hasWarning) {
      card.classList.add("budget-warning");
      
      // Add pulsing animation
      card.style.animation = "budgetWarningPulse 1s ease-in-out 2";
      
      setTimeout(() => {
        card.style.animation = "";
      }, 2000);
    } else {
      card.classList.remove("budget-warning");
    }
  }

  /**
   * Get formatted percentage string
   * @param {number} value - Percentage value
   * @param {number} decimals - Number of decimal places
   * @returns {string} Formatted percentage
   */
  formatPercentage(value, decimals = 1) {
    return `${value.toFixed(decimals)}%`;
  }

  /**
   * Get color for percentage value (for visual feedback)
   * @param {number} percentage - Percentage value
   * @returns {string} CSS color value
   */
  getPercentageColor(percentage) {
    if (percentage === 0) return "#94a3b8"; // Gray for zero
    if (percentage < 10) return "#ef4444"; // Red for low
    if (percentage < 25) return "#f59e0b"; // Orange for medium-low
    if (percentage < 50) return "#eab308"; // Yellow for medium
    if (percentage < 75) return "#22c55e"; // Green for good
    return "#059669"; // Dark green for high
  }

  /**
   * Update card visual state based on allocation
   * @param {HTMLElement} card - Card element
   * @param {number} percentage - Allocation percentage
   */
  updateCardVisualState(card, percentage) {
    if (!card) return;

    // Update opacity based on allocation
    const baseOpacity = percentage > 0 ? 1 : 0.5;
    card.style.setProperty("--card-opacity", baseOpacity);

    // Update border color based on allocation level
    const color = this.getPercentageColor(percentage);
    card.style.setProperty("--allocation-color", color);

    // Add allocation level class
    card.classList.remove("low-allocation", "medium-allocation", "high-allocation");
    
    if (percentage > 0) {
      if (percentage < 15) {
        card.classList.add("low-allocation");
      } else if (percentage < 40) {
        card.classList.add("medium-allocation");
      } else {
        card.classList.add("high-allocation");
      }
    }
  }

  /**
   * Create and show a temporary notification
   * @param {string} message - Notification message
   * @param {string} type - Notification type (success, warning, error)
   * @param {number} duration - Duration in milliseconds
   */
  showNotification(message, type = "info", duration = 3000) {
    const notification = document.createElement("div");
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
      position: "fixed",
      top: "20px",
      right: "20px",
      padding: "12px 20px",
      borderRadius: "8px",
      color: "white",
      fontWeight: "500",
      zIndex: "10000",
      transform: "translateX(100%)",
      transition: "transform 0.3s ease",
    });

    // Set background color based on type
    const colors = {
      success: "#10b981",
      warning: "#f59e0b",
      error: "#ef4444",
      info: "#3b82f6",
    };
    notification.style.backgroundColor = colors[type] || colors.info;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = "translateX(0)";
    }, 10);

    // Animate out and remove
    setTimeout(() => {
      notification.style.transform = "translateX(100%)";
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, duration);
  }

  /**
   * Parse currency string to number
   * @param {string} value - Currency string
   * @returns {number} Parsed number
   */
  parseCurrency(value) {
    return parseFloat(value.replace(/[^\d,.-]/g, "").replace(",", "."));
  }

  /**
   * Cleanup animations and timers
   */
  destroy() {
    // Clear all pending animations
    for (const timeout of this.animationQueue.values()) {
      clearTimeout(timeout);
    }
    this.animationQueue.clear();
  }
}

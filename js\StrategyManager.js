/**
 * StrategyManager - <PERSON>les dynamic investment strategy interpretation
 * Analyzes portfolio allocations and provides intelligent strategy insights
 */
export class StrategyManager {
  constructor() {
    this.strategies = this.initializeStrategies();
    this.currentStrategy = null;
    this.strategyElement = document.getElementById("strategy-text");
  }

  /**
   * Initialize enhanced strategy definitions with more sophisticated rules
   */
  initializeStrategies() {
    return {
      ultraConservative: {
        priority: 1,
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, ["renda-fixa"]);
          const savings = this.getAssetTotal(allocations, "Poupança");
          const totalSafe = fixedIncome + savings;
          return totalSafe > 80 && fixedIncome > 60;
        },
        text: "Perfil ultra-conservador - Máxima segurança com foco em preservação de capital e renda fixa garantida.",
        icon: "🛡️",
        color: "#10b981"
      },

      conservative: {
        priority: 2,
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, ["renda-fixa"]);
          const liquidityFunds = this.getAssetTotal(allocations, "Liquidez") + 
                                this.getAssetTotal(allocations, "Liquidez2");
          return fixedIncome > 50 && fixedIncome <= 80 && liquidityFunds > 15;
        },
        text: "Perfil conservador - Priorizando segurança e liquidez com foco em renda fixa e reserva de emergência.",
        icon: "🏦",
        color: "#3b82f6"
      },

      moderateConservative: {
        priority: 3,
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, ["renda-fixa"]);
          const funds = this.getCategoryTotal(allocations, ["fundos"]);
          return fixedIncome >= 40 && fixedIncome <= 60 && funds >= 20 && funds <= 40;
        },
        text: "Perfil moderadamente conservador - Equilibrio entre segurança e crescimento moderado.",
        icon: "⚖️",
        color: "#8b5cf6"
      },

      balanced: {
        priority: 4,
        condition: (allocations) => {
          const fixedIncome = this.getCategoryTotal(allocations, ["renda-fixa"]);
          const equity = this.getCategoryTotal(allocations, ["fundos"]);
          const alternatives = this.getCategoryTotal(allocations, ["outros"]);
          
          return (
            fixedIncome >= 30 && fixedIncome <= 50 &&
            equity >= 30 && equity <= 50 &&
            alternatives <= 20
          );
        },
        text: "Perfil equilibrado - Diversificação balanceada entre renda fixa e variável para crescimento sustentável.",
        icon: "⚖️",
        color: "#f59e0b"
      },

      moderateAggressive: {
        priority: 5,
        condition: (allocations) => {
          const equity = this.getCategoryTotal(allocations, ["fundos"]);
          const fixedIncome = this.getCategoryTotal(allocations, ["renda-fixa"]);
          return equity > 50 && equity <= 70 && fixedIncome >= 20;
        },
        text: "Perfil moderadamente agressivo - Foco em crescimento com alguma proteção em renda fixa.",
        icon: "📈",
        color: "#ef4444"
      },

      aggressive: {
        priority: 6,
        condition: (allocations) => {
          const equity = this.getCategoryTotal(allocations, ["fundos"]);
          const fixedIncome = this.getCategoryTotal(allocations, ["renda-fixa"]);
          return equity > 70 && fixedIncome < 20;
        },
        text: "Perfil agressivo - Foco em crescimento de longo prazo com alta exposição a renda variável.",
        icon: "🚀",
        color: "#dc2626"
      },

      liquidityFocused: {
        priority: 7,
        condition: (allocations) => {
          const liquidityFunds = this.getAssetTotal(allocations, "Liquidez") + 
                                this.getAssetTotal(allocations, "Liquidez2");
          const savings = this.getAssetTotal(allocations, "Poupança");
          return liquidityFunds > 30 || savings > 25;
        },
        text: "Estratégia de liquidez - Mantendo alta reserva para oportunidades e emergências financeiras.",
        icon: "💧",
        color: "#06b6d4"
      },

      alternativeInvestor: {
        priority: 8,
        condition: (allocations) => {
          const alternatives = this.getCategoryTotal(allocations, ["outros"]);
          const realEstate = this.getAssetTotal(allocations, "Imóvel");
          return alternatives > 25 || realEstate > 20;
        },
        text: "Investidor em alternativos - Diversificando com ativos não tradicionais e investimentos imobiliários.",
        icon: "🏢",
        color: "#7c3aed"
      },

      retirementFocused: {
        priority: 9,
        condition: (allocations) => {
          const retirement = this.getAssetTotal(allocations, "Previdência");
          return retirement > 20;
        },
        text: "Foco em aposentadoria - Construindo patrimônio para o futuro com previdência privada.",
        icon: "🏖️",
        color: "#059669"
      },

      highlyDiversified: {
        priority: 10,
        condition: (allocations) => {
          const categories = ["renda-fixa", "fundos", "outros"];
          const activeCats = categories.filter(
            (cat) => this.getCategoryTotal(allocations, [cat]) > 10
          );
          const totalAssets = this.countActiveAssets(allocations);
          return activeCats.length >= 3 && totalAssets >= 5;
        },
        text: "Alta diversificação - Distribuindo riscos entre múltiplas classes de ativos e estratégias.",
        icon: "🌐",
        color: "#6366f1"
      },

      emergencyBuilder: {
        priority: 11,
        condition: (allocations) => {
          const emergency = this.getAssetTotal(allocations, "Liquidez") + 
                           this.getAssetTotal(allocations, "Liquidez2") +
                           this.getAssetTotal(allocations, "Poupança");
          return emergency > 40;
        },
        text: "Construção de reserva - Priorizando a formação de uma sólida reserva de emergência.",
        icon: "🆘",
        color: "#f97316"
      },

      opportunitySeeker: {
        priority: 12,
        condition: (allocations) => {
          const totalAllocated = Array.from(Object.values(allocations)).reduce((sum, val) => sum + val, 0);
          return totalAllocated < 70 && totalAllocated > 30;
        },
        text: "Buscador de oportunidades - Mantendo flexibilidade para aproveitar oportunidades de mercado.",
        icon: "🎯",
        color: "#84cc16"
      },

      beginner: {
        priority: 13,
        condition: (allocations) => {
          const totalAllocated = Array.from(Object.values(allocations)).reduce((sum, val) => sum + val, 0);
          const activeAssets = this.countActiveAssets(allocations);
          return totalAllocated < 30 && activeAssets <= 2;
        },
        text: "Perfil iniciante - Começando a jornada de investimentos com alocações simples e seguras.",
        icon: "🌱",
        color: "#22c55e"
      },

      default: {
        priority: 999,
        condition: () => true,
        text: "Continue alocando para descobrir sua estratégia de investimento personalizada.",
        icon: "💡",
        color: "#6b7280"
      },
    };
  }

  /**
   * Update dynamic strategy text with enhanced analysis
   * @param {Object} allocations - Current allocations object
   */
  updateStrategyText(allocations = {}) {
    if (!this.strategyElement) return;

    // Find the best matching strategy based on priority
    const matchingStrategies = Object.entries(this.strategies)
      .filter(([key, strategy]) => strategy.condition(allocations))
      .sort((a, b) => a[1].priority - b[1].priority);

    const strategy = matchingStrategies.length > 0 
      ? matchingStrategies[0][1] 
      : this.strategies.default;

    // Only update if strategy changed to avoid unnecessary animations
    if (this.currentStrategy !== strategy) {
      this.currentStrategy = strategy;
      this.animateStrategyChange(strategy);
    }
  }

  /**
   * Animate strategy text change with enhanced effects
   * @param {Object} strategy - Strategy object
   */
  animateStrategyChange(strategy) {
    // Fade out current text
    this.strategyElement.style.opacity = "0.3";
    this.strategyElement.style.transform = "translateY(5px)";
    this.strategyElement.style.transition = "all 0.2s ease";

    setTimeout(() => {
      // Update content with icon and enhanced formatting
      this.strategyElement.innerHTML = `
        <div class="strategy-content">
          <span class="strategy-icon" style="color: ${strategy.color}">${strategy.icon}</span>
          <span class="strategy-text">${strategy.text}</span>
        </div>
      `;

      // Add strategy-specific styling
      this.strategyElement.setAttribute('data-strategy-type', this.getStrategyType(strategy));

      // Fade in new text
      this.strategyElement.style.opacity = "1";
      this.strategyElement.style.transform = "translateY(0)";
    }, 200);
  }

  /**
   * Get strategy type for CSS styling
   * @param {Object} strategy - Strategy object
   * @returns {string} Strategy type
   */
  getStrategyType(strategy) {
    if (strategy.text.includes("conservador")) return "conservative";
    if (strategy.text.includes("agressivo")) return "aggressive";
    if (strategy.text.includes("equilibrado")) return "balanced";
    if (strategy.text.includes("liquidez")) return "liquidity";
    if (strategy.text.includes("diversificação")) return "diversified";
    return "default";
  }

  /**
   * Get total allocation for specific categories
   * @param {Object} allocations - Allocations object
   * @param {Array} categories - Array of category names
   * @returns {number} Total percentage
   */
  getCategoryTotal(allocations, categories) {
    let total = 0;
    for (const [asset, value] of Object.entries(allocations)) {
      const card = document.querySelector(`[data-asset="${asset}"]`);
      if (card) {
        const category = card.dataset.category;
        if (categories.includes(category)) {
          total += value;
        }
      }
    }
    return total;
  }

  /**
   * Get total allocation for specific asset type
   * @param {Object} allocations - Allocations object
   * @param {string} assetType - Asset type name
   * @returns {number} Total percentage
   */
  getAssetTotal(allocations, assetType) {
    let total = 0;
    for (const [asset, value] of Object.entries(allocations)) {
      const card = document.querySelector(`[data-asset="${asset}"]`);
      if (card) {
        const cardAssetType = card.querySelector(".asset-type")?.textContent?.trim() ||
                             card.querySelector(".asset-type-text")?.textContent?.trim();
        if (cardAssetType === assetType) {
          total += value;
        }
      }
    }
    return total;
  }

  /**
   * Count number of active assets (with allocation > 0)
   * @param {Object} allocations - Allocations object
   * @returns {number} Number of active assets
   */
  countActiveAssets(allocations) {
    return Object.values(allocations).filter(value => value > 0).length;
  }

  /**
   * Get detailed portfolio analysis
   * @param {Object} allocations - Allocations object
   * @returns {Object} Analysis object
   */
  getPortfolioAnalysis(allocations) {
    const totalAllocated = Object.values(allocations).reduce((sum, val) => sum + val, 0);
    const categories = {
      "renda-fixa": this.getCategoryTotal(allocations, ["renda-fixa"]),
      "fundos": this.getCategoryTotal(allocations, ["fundos"]),
      "outros": this.getCategoryTotal(allocations, ["outros"])
    };

    return {
      totalAllocated,
      categories,
      activeAssets: this.countActiveAssets(allocations),
      diversificationScore: this.calculateDiversificationScore(categories),
      riskLevel: this.calculateRiskLevel(categories),
      currentStrategy: this.currentStrategy
    };
  }

  /**
   * Calculate diversification score (0-100)
   * @param {Object} categories - Category allocations
   * @returns {number} Diversification score
   */
  calculateDiversificationScore(categories) {
    const activeCategories = Object.values(categories).filter(val => val > 0).length;
    const maxCategory = Math.max(...Object.values(categories));
    
    // Higher score for more categories and more balanced distribution
    const categoryScore = (activeCategories / 3) * 50;
    const balanceScore = (100 - maxCategory) * 0.5;
    
    return Math.min(100, categoryScore + balanceScore);
  }

  /**
   * Calculate risk level (1-10)
   * @param {Object} categories - Category allocations
   * @returns {number} Risk level
   */
  calculateRiskLevel(categories) {
    const equityWeight = categories["fundos"] * 0.1;
    const fixedIncomeWeight = categories["renda-fixa"] * 0.02;
    const alternativeWeight = categories["outros"] * 0.08;
    
    return Math.min(10, Math.max(1, equityWeight + alternativeWeight - fixedIncomeWeight + 3));
  }

  /**
   * Get strategy recommendations based on current allocation
   * @param {Object} allocations - Current allocations
   * @returns {Array} Array of recommendation objects
   */
  getRecommendations(allocations) {
    const analysis = this.getPortfolioAnalysis(allocations);
    const recommendations = [];

    if (analysis.totalAllocated < 50) {
      recommendations.push({
        type: "allocation",
        message: "Considere aumentar sua alocação total para otimizar o crescimento do patrimônio.",
        priority: "high"
      });
    }

    if (analysis.diversificationScore < 40) {
      recommendations.push({
        type: "diversification",
        message: "Diversifique mais entre diferentes classes de ativos para reduzir riscos.",
        priority: "medium"
      });
    }

    if (analysis.categories["fundos"] === 0 && analysis.totalAllocated > 30) {
      recommendations.push({
        type: "growth",
        message: "Considere adicionar fundos de investimento para potencial de crescimento.",
        priority: "medium"
      });
    }

    return recommendations;
  }
}

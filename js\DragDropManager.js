/**
 * DragDropManager - Handles drag and drop functionality with SortableJS
 * Manages card/chip transformations and grid interactions
 */
export class DragDropManager {
  constructor(sliderManager, tooltipManager, valueDisplayManager) {
    this.sliderManager = sliderManager;
    this.tooltipManager = tooltipManager;
    this.valueDisplayManager = valueDisplayManager;
    this.sortableInstances = new Map();
  }

  /**
   * Initialize drag and drop functionality
   */
  init() {
    this.setupMainGridSortable();
    this.setupSidebarChipsSortable();
  }

  /**
   * Setup sortable functionality for the main grid
   */
  setupMainGridSortable() {
    const mainGrid = document.getElementById("main-grid");
    if (!mainGrid) return;

    const sortableInstance = Sortable.create(mainGrid, {
      group: {
        name: "portfolio",
        pull: true,
        put: true,
      },
      animation: 300,
      ghostClass: "sortable-ghost",
      chosenClass: "sortable-chosen",
      dragClass: "sortable-drag",
      handle: ".card-header, .asset-type, .allocation-value",
      
      // Enhanced drag threshold for better mobile experience
      delay: 100,
      delayOnTouchStart: true,
      touchStartThreshold: 5,

      onStart: (evt) => {
        mainGrid.classList.add("drag-over");
        this.sliderManager.setSliderInteractionState(false);
        this.addDragFeedback(evt.item);
      },

      onEnd: (evt) => {
        mainGrid.classList.remove("drag-over");
        this.sliderManager.setSliderInteractionState(true);
        this.removeDragFeedback(evt.item);
        this.handleMainGridDrop();
      },

      onAdd: (evt) => {
        if (evt.item.classList.contains("asset-chip")) {
          this.transformChipToCard(evt.item, evt.newIndex);
        }
      },

      onRemove: (evt) => {
        if (evt.item.classList.contains("asset-card")) {
          this.transformCardToChip(evt.item, evt.from);
        }
      },

      onMove: (evt) => {
        return this.validateMove(evt);
      },
    });

    this.sortableInstances.set("main-grid", sortableInstance);
  }

  /**
   * Setup sortable functionality for sidebar chips
   */
  setupSidebarChipsSortable() {
    const chipContainers = document.querySelectorAll(".asset-chips");

    chipContainers.forEach((container, index) => {
      const sortableInstance = Sortable.create(container, {
        group: {
          name: "portfolio",
          pull: "clone",
          put: true,
        },
        animation: 300,
        ghostClass: "sortable-ghost",
        chosenClass: "sortable-chosen",
        dragClass: "sortable-drag",
        
        // Enhanced settings for chips
        sort: false, // Prevent sorting within chip containers
        delay: 100,
        delayOnTouchStart: true,

        onStart: (evt) => {
          container.classList.add("drag-over");
          this.addChipDragFeedback(evt.item);
        },

        onEnd: (evt) => {
          container.classList.remove("drag-over");
          this.removeChipDragFeedback(evt.item);
        },

        onAdd: (evt) => {
          if (evt.item.classList.contains("asset-card")) {
            this.transformCardToChip(evt.item, evt.to);
          }
        },

        onClone: (evt) => {
          this.enhanceClonedChip(evt.clone);
        },
      });

      this.sortableInstances.set(`chips-${index}`, sortableInstance);
    });
  }

  /**
   * Add visual feedback during drag operations
   * @param {HTMLElement} item - Dragged item
   */
  addDragFeedback(item) {
    item.classList.add("dragging");
    
    // Add glow effect
    item.style.boxShadow = "0 8px 25px rgba(196, 151, 37, 0.3)";
    item.style.transform = "rotate(2deg) scale(1.02)";
    
    // Dim other items
    const siblings = Array.from(item.parentNode.children).filter(child => child !== item);
    siblings.forEach(sibling => {
      sibling.style.opacity = "0.6";
      sibling.style.transition = "opacity 0.2s ease";
    });
  }

  /**
   * Remove visual feedback after drag operations
   * @param {HTMLElement} item - Dragged item
   */
  removeDragFeedback(item) {
    item.classList.remove("dragging");
    item.style.boxShadow = "";
    item.style.transform = "";
    
    // Restore opacity for all items
    const allItems = document.querySelectorAll(".asset-card, .asset-chip");
    allItems.forEach(item => {
      item.style.opacity = "";
      item.style.transition = "";
    });
  }

  /**
   * Add visual feedback for chip dragging
   * @param {HTMLElement} chip - Dragged chip
   */
  addChipDragFeedback(chip) {
    chip.classList.add("chip-dragging");
    chip.style.transform = "scale(1.1) rotate(-2deg)";
    chip.style.zIndex = "1000";
  }

  /**
   * Remove visual feedback for chip dragging
   * @param {HTMLElement} chip - Dragged chip
   */
  removeChipDragFeedback(chip) {
    chip.classList.remove("chip-dragging");
    chip.style.transform = "";
    chip.style.zIndex = "";
  }

  /**
   * Enhance cloned chip appearance
   * @param {HTMLElement} clone - Cloned chip element
   */
  enhanceClonedChip(clone) {
    clone.style.opacity = "0.8";
    clone.style.border = "2px dashed #c49725";
    clone.style.backgroundColor = "rgba(196, 151, 37, 0.1)";
  }

  /**
   * Validate move operations
   * @param {Object} evt - Move event
   * @returns {boolean} Whether move is valid
   */
  validateMove(evt) {
    // Prevent dropping cards on chips and vice versa in invalid locations
    const draggedElement = evt.dragged;
    const targetContainer = evt.to;

    if (draggedElement.classList.contains("asset-chip") && 
        targetContainer.classList.contains("asset-chips")) {
      return false; // Don't allow chips to be dropped back in chip containers
    }

    return true;
  }

  /**
   * Transform chip to card with enhanced animation
   * @param {HTMLElement} chipElement - Chip element to transform
   * @param {number} gridIndex - Grid index position
   */
  transformChipToCard(chipElement, gridIndex) {
    const assetId = chipElement.dataset.assetId;
    const category = chipElement.dataset.category;
    const assetType = chipElement.dataset.assetType;

    // Create card HTML
    const cardHTML = this.createCardHTML(assetId, category, assetType);
    
    // Add transformation animation
    chipElement.classList.add("transforming-to-card");
    
    setTimeout(() => {
      chipElement.outerHTML = cardHTML;
      
      const newCard = document.querySelector(`[data-asset-id="${assetId}"]`);
      if (newCard) {
        newCard.classList.add("card-appear-animation");
        
        // Setup the new card's slider
        this.setupNewCardSlider(newCard);
        
        // Remove animation class after completion
        setTimeout(() => {
          newCard.classList.remove("card-appear-animation");
        }, 400);
      }
    }, 150);

    // Update remaining amount
    this.valueDisplayManager.updateRemainingAmount(
      this.sliderManager.getAllocations(),
      this.sliderManager.totalCapital
    );
  }

  /**
   * Transform card to chip with enhanced animation
   * @param {HTMLElement} cardElement - Card element to transform
   * @param {HTMLElement} targetContainer - Target container
   */
  transformCardToChip(cardElement, targetContainer) {
    const assetId = cardElement.dataset.assetId;
    const category = cardElement.dataset.category;
    const assetTypeElement = cardElement.querySelector(".asset-type") || 
                            cardElement.querySelector(".asset-type-text");
    const assetType = assetTypeElement ? assetTypeElement.textContent.trim() : 
                     cardElement.dataset.assetType;

    // Reset allocation for this asset
    const assetName = cardElement.dataset.asset;
    if (this.sliderManager.allocations.has(assetName)) {
      this.sliderManager.allocations.set(assetName, 0);
    }

    // Create chip HTML
    const chipHTML = this.createChipHTML(assetId, category, assetType);
    
    // Add transformation animation
    cardElement.classList.add("transforming-to-chip");

    setTimeout(() => {
      cardElement.outerHTML = chipHTML;
      
      // Update displays
      this.valueDisplayManager.updateRemainingAmount(
        this.sliderManager.getAllocations(),
        this.sliderManager.totalCapital
      );
      
      // Dispatch strategy update event
      document.dispatchEvent(new CustomEvent("allocationChange", {
        detail: {
          assetName,
          value: 0,
          isFinal: true,
          allocations: Object.fromEntries(this.sliderManager.getAllocations()),
        },
      }));
    }, 300);
  }

  /**
   * Create enhanced card HTML
   * @param {string} assetId - Asset ID
   * @param {string} category - Asset category
   * @param {string} assetType - Asset type
   * @returns {string} Card HTML
   */
  createCardHTML(assetId, category, assetType) {
    const categoryLabel = this.getCategoryLabel(category);
    const assetName = assetId.replace("-available", "").replace("-", "_");

    return `
      <div class="asset-card" data-category="${category}" data-asset="${assetName}" data-asset-id="${assetId}" data-asset-type="${assetType}">
        <div class="card-header">
          <div class="header-content">
            <div class="category-asset-line">
              ${categoryLabel}<span class="separator">></span><span class="asset-type-text">${assetType}</span>
            </div>
          </div>
          <i class="fas fa-edit edit-icon"></i>
        </div>
        <div class="asset-type" style="display: none;">${assetType}</div>
        <div class="allocation-value">R$0</div>
        <div class="slider-container">
          <range-slider
            class="allocation-slider"
            min="0"
            max="100"
            value="0"
            data-asset="${assetName}"
            aria-label="${assetType} allocation percentage"
          ></range-slider>
        </div>
      </div>
    `;
  }

  /**
   * Create enhanced chip HTML
   * @param {string} assetId - Asset ID
   * @param {string} category - Asset category
   * @param {string} assetType - Asset type
   * @returns {string} Chip HTML
   */
  createChipHTML(assetId, category, assetType) {
    return `
      <div class="asset-chip" draggable="true" data-asset-id="${assetId}" data-category="${category}" data-asset-type="${assetType}">
        <i class="fas fa-grip-vertical drag-handle"></i>
        <span>${assetType}</span>
      </div>
    `;
  }

  /**
   * Get category label for display
   * @param {string} category - Category key
   * @returns {string} Display label
   */
  getCategoryLabel(category) {
    const labels = {
      "renda-fixa": "Renda fixa",
      fundos: "Fund. de Investimento",
      outros: "Outros",
    };
    return labels[category] || category;
  }

  /**
   * Setup slider for newly created card
   * @param {HTMLElement} card - New card element
   */
  setupNewCardSlider(card) {
    const slider = card.querySelector(".allocation-slider");
    if (slider) {
      // Setup tooltip for new slider
      this.tooltipManager.setupSingleTooltip(slider);
      
      // Setup slider functionality
      this.sliderManager.setupSingleSlider(slider);
      
      // Initialize display values
      const allocationValue = card.querySelector(".allocation-value");
      if (allocationValue) {
        this.valueDisplayManager.updateValueDisplay(allocationValue, 0, false);
      }
    }
  }

  /**
   * Handle main grid drop completion
   */
  handleMainGridDrop() {
    // Update all displays after drop
    this.valueDisplayManager.updateRemainingAmount(
      this.sliderManager.getAllocations(),
      this.sliderManager.totalCapital
    );
    
    // Dispatch update event
    document.dispatchEvent(new CustomEvent("allocationChange", {
      detail: {
        allocations: Object.fromEntries(this.sliderManager.getAllocations()),
        isFinal: true,
      },
    }));
  }

  /**
   * Cleanup sortable instances
   */
  destroy() {
    for (const instance of this.sortableInstances.values()) {
      instance.destroy();
    }
    this.sortableInstances.clear();
  }

  /**
   * Enable/disable drag and drop
   * @param {boolean} enabled - Whether drag and drop should be enabled
   */
  setDragDropEnabled(enabled) {
    for (const instance of this.sortableInstances.values()) {
      instance.option("disabled", !enabled);
    }
  }
}

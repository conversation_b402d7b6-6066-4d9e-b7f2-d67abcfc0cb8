/**
 * Main entry point for the Portfolio Allocation Interface
 * Initializes the PortfolioManager and handles global application setup
 */
import { PortfolioManager } from './PortfolioManager.js';

// Global application state
let portfolioManager = null;
let isApplicationReady = false;

/**
 * Initialize the application
 */
async function initializeApplication() {
  try {
    console.log("Initializing Portfolio Allocation Interface...");
    
    // Show loading indicator
    showLoadingIndicator();
    
    // Create and initialize portfolio manager
    portfolioManager = new PortfolioManager();
    await portfolioManager.init();
    
    // Setup global error handling
    setupErrorHandling();
    
    // Setup development tools (if in development mode)
    if (isDevelopmentMode()) {
      setupDevelopmentTools();
    }
    
    // Hide loading indicator
    hideLoadingIndicator();
    
    // Mark application as ready
    isApplicationReady = true;
    
    console.log("Portfolio Allocation Interface ready!");
    
    // Show welcome message
    showWelcomeMessage();
    
  } catch (error) {
    console.error("Failed to initialize application:", error);
    showErrorMessage("Falha ao inicializar a aplicação. Por favor, recarregue a página.");
    hideLoadingIndicator();
  }
}

/**
 * Show loading indicator
 */
function showLoadingIndicator() {
  const loadingDiv = document.createElement("div");
  loadingDiv.id = "loading-indicator";
  loadingDiv.innerHTML = `
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">Carregando interface...</div>
    </div>
  `;
  
  // Style the loading indicator
  loadingDiv.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(2px);
  `;
  
  const style = document.createElement("style");
  style.textContent = `
    .loading-content {
      text-align: center;
      color: #1e293b;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e2e8f0;
      border-top: 4px solid #c49725;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }
    
    .loading-text {
      font-size: 16px;
      font-weight: 500;
      color: #64748b;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  
  document.head.appendChild(style);
  document.body.appendChild(loadingDiv);
}

/**
 * Hide loading indicator
 */
function hideLoadingIndicator() {
  const loadingDiv = document.getElementById("loading-indicator");
  if (loadingDiv) {
    loadingDiv.style.opacity = "0";
    loadingDiv.style.transition = "opacity 0.3s ease";
    
    setTimeout(() => {
      if (loadingDiv.parentNode) {
        loadingDiv.parentNode.removeChild(loadingDiv);
      }
    }, 300);
  }
}

/**
 * Show welcome message
 */
function showWelcomeMessage() {
  // Only show on first visit
  if (!localStorage.getItem("portfolio-welcome-shown")) {
    setTimeout(() => {
      if (portfolioManager && portfolioManager.valueDisplayManager) {
        portfolioManager.valueDisplayManager.showNotification(
          "Bem-vindo! Arraste ativos ou use os sliders para alocar seu patrimônio.",
          "info",
          5000
        );
        localStorage.setItem("portfolio-welcome-shown", "true");
      }
    }, 1000);
  }
}

/**
 * Setup global error handling
 */
function setupErrorHandling() {
  // Handle unhandled promise rejections
  window.addEventListener("unhandledrejection", (event) => {
    console.error("Unhandled promise rejection:", event.reason);
    
    if (portfolioManager && portfolioManager.valueDisplayManager) {
      portfolioManager.valueDisplayManager.showNotification(
        "Ocorreu um erro inesperado. Algumas funcionalidades podem não funcionar corretamente.",
        "error"
      );
    }
    
    // Prevent the default browser error handling
    event.preventDefault();
  });
  
  // Handle general JavaScript errors
  window.addEventListener("error", (event) => {
    console.error("JavaScript error:", event.error);
    
    // Don't show notifications for minor errors
    if (event.error && event.error.name !== "TypeError") {
      if (portfolioManager && portfolioManager.valueDisplayManager) {
        portfolioManager.valueDisplayManager.showNotification(
          "Erro detectado. A aplicação pode não funcionar corretamente.",
          "warning"
        );
      }
    }
  });
}

/**
 * Check if in development mode
 * @returns {boolean} True if in development mode
 */
function isDevelopmentMode() {
  return location.hostname === "localhost" || 
         location.hostname === "127.0.0.1" || 
         location.search.includes("debug=true");
}

/**
 * Setup development tools
 */
function setupDevelopmentTools() {
  // Add global reference for debugging
  window.portfolioManager = portfolioManager;
  
  // Add keyboard shortcuts for development
  document.addEventListener("keydown", (e) => {
    // Ctrl+Shift+D: Show debug info
    if (e.ctrlKey && e.shiftKey && e.key === "D") {
      e.preventDefault();
      showDebugInfo();
    }
    
    // Ctrl+Shift+E: Export portfolio
    if (e.ctrlKey && e.shiftKey && e.key === "E") {
      e.preventDefault();
      exportPortfolioData();
    }
    
    // Ctrl+Shift+I: Import portfolio
    if (e.ctrlKey && e.shiftKey && e.key === "I") {
      e.preventDefault();
      importPortfolioData();
    }
  });
  
  console.log("Development tools enabled:");
  console.log("- Ctrl+Shift+D: Show debug info");
  console.log("- Ctrl+Shift+E: Export portfolio");
  console.log("- Ctrl+Shift+I: Import portfolio");
  console.log("- window.portfolioManager: Access to portfolio manager");
}

/**
 * Show debug information
 */
function showDebugInfo() {
  if (!portfolioManager) return;
  
  const metrics = portfolioManager.getPerformanceMetrics();
  const state = portfolioManager.getPortfolioState();
  
  console.group("Portfolio Debug Info");
  console.log("Performance Metrics:", metrics);
  console.log("Portfolio State:", state);
  console.log("DOM Elements:", {
    sliders: document.querySelectorAll(".allocation-slider").length,
    cards: document.querySelectorAll(".asset-card").length,
    chips: document.querySelectorAll(".asset-chip").length,
  });
  console.groupEnd();
  
  // Show in UI as well
  const debugInfo = `
    Sliders: ${metrics.totalSliders}
    Active Tooltips: ${metrics.activeTooltips}
    Active Edits: ${metrics.activeEdits}
    Memory: ${metrics.memoryUsage ? `${metrics.memoryUsage.used}MB` : 'N/A'}
  `;
  
  alert(`Debug Info:\n${debugInfo}`);
}

/**
 * Export portfolio data
 */
function exportPortfolioData() {
  if (!portfolioManager) return;
  
  try {
    const data = portfolioManager.exportPortfolio();
    const blob = new Blob([data], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement("a");
    a.href = url;
    a.download = `portfolio-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    URL.revokeObjectURL(url);
    
    portfolioManager.valueDisplayManager.showNotification(
      "Portfolio exportado com sucesso!",
      "success"
    );
  } catch (error) {
    console.error("Export failed:", error);
    portfolioManager.valueDisplayManager.showNotification(
      "Erro ao exportar portfolio",
      "error"
    );
  }
}

/**
 * Import portfolio data
 */
function importPortfolioData() {
  if (!portfolioManager) return;
  
  const input = document.createElement("input");
  input.type = "file";
  input.accept = ".json";
  
  input.onchange = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        portfolioManager.importPortfolio(e.target.result);
      } catch (error) {
        console.error("Import failed:", error);
        portfolioManager.valueDisplayManager.showNotification(
          "Erro ao importar portfolio",
          "error"
        );
      }
    };
    reader.readAsText(file);
  };
  
  input.click();
}

/**
 * Show error message
 * @param {string} message - Error message
 */
function showErrorMessage(message) {
  const errorDiv = document.createElement("div");
  errorDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #ef4444;
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    font-weight: 500;
    z-index: 10001;
    max-width: 400px;
  `;
  errorDiv.textContent = message;
  
  document.body.appendChild(errorDiv);
  
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 5000);
}

/**
 * Handle page visibility changes
 */
document.addEventListener("visibilitychange", () => {
  if (!document.hidden && isApplicationReady && portfolioManager) {
    // Refresh displays when page becomes visible again
    portfolioManager.updateAllDisplays();
  }
});

/**
 * Handle page unload
 */
window.addEventListener("beforeunload", () => {
  if (portfolioManager) {
    portfolioManager.destroy();
  }
});

// Initialize when DOM is ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initializeApplication);
} else {
  initializeApplication();
}

// Export for global access
export { portfolioManager, isApplicationReady };

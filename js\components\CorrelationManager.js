/**
 * CorrelationManager - Handles visual correlation effects between sliders
 * Creates visual feedback when sliders are moved to simulate asset correlation
 */
import { eventBus } from '../core/EventBus.js';

export class CorrelationManager {
  constructor() {
    this.isEnabled = true;
    this.effectIntensity = 0.02;
    this.effectDuration = 300;
    this.activeEffects = new Set();
    
    this.init();
  }

  /**
   * Initialize correlation manager
   */
  init() {
    this.setupEventListeners();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    eventBus.on('slider:correlation', this.handleCorrelationTrigger.bind(this));
  }

  /**
   * Handle correlation trigger from slider
   * @param {Object} data - Trigger data
   */
  handleCorrelationTrigger(data) {
    if (!this.isEnabled) return;
    
    this.triggerVisualCorrelation(data.sourceAsset);
  }

  /**
   * Trigger visual correlation effect
   * @param {string} sourceAsset - Asset that triggered the effect
   */
  triggerVisualCorrelation(sourceAsset) {
    const allSliders = document.querySelectorAll('range-slider.allocation-slider');
    const otherSliders = Array.from(allSliders).filter(
      slider => slider.dataset.asset !== sourceAsset
    );

    if (otherSliders.length === 0) return;

    // Select 1-2 random sliders for correlation effect
    const numCorrelated = Math.min(2, otherSliders.length);
    const correlatedSliders = this.getRandomSliders(otherSliders, numCorrelated);

    correlatedSliders.forEach((slider, index) => {
      const card = slider.closest('.asset-card');
      const delay = index * 50; // Stagger animations

      setTimeout(() => {
        this.applyCorrelationEffect(card, slider.dataset.asset);
      }, delay);
    });
  }

  /**
   * Apply correlation effect to a card
   * @param {HTMLElement} card - Card element
   * @param {string} assetName - Asset name
   */
  applyCorrelationEffect(card, assetName) {
    // Prevent multiple effects on same card
    if (this.activeEffects.has(assetName)) return;
    
    this.activeEffects.add(assetName);

    // Apply visual effects
    const originalTransform = card.style.transform;
    const originalBoxShadow = card.style.boxShadow;
    const originalBorderColor = card.style.borderColor;
    const originalTransition = card.style.transition;

    // Enhanced visual effects
    card.style.transform = 'scale(1.02) translateY(-2px)';
    card.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease';
    card.style.boxShadow = '0 8px 25px rgba(196, 151, 37, 0.15)';
    card.style.borderColor = 'rgba(196, 151, 37, 0.3)';

    // Add correlation indicator
    this.addCorrelationIndicator(card);

    // Reset after animation
    setTimeout(() => {
      card.style.transform = originalTransform;
      card.style.boxShadow = originalBoxShadow;
      card.style.borderColor = originalBorderColor;
      card.style.transition = originalTransition;
      
      this.removeCorrelationIndicator(card);
      this.activeEffects.delete(assetName);
    }, this.effectDuration);
  }

  /**
   * Add correlation indicator to card
   * @param {HTMLElement} card - Card element
   */
  addCorrelationIndicator(card) {
    const indicator = document.createElement('div');
    indicator.className = 'correlation-indicator';
    indicator.innerHTML = '↗️';
    indicator.style.cssText = `
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 12px;
      opacity: 0.7;
      animation: correlationPulse 0.3s ease;
      z-index: 10;
    `;

    card.style.position = 'relative';
    card.appendChild(indicator);
  }

  /**
   * Remove correlation indicator from card
   * @param {HTMLElement} card - Card element
   */
  removeCorrelationIndicator(card) {
    const indicator = card.querySelector('.correlation-indicator');
    if (indicator) {
      indicator.remove();
    }
  }

  /**
   * Get random sliders for correlation effect
   * @param {Array} sliders - Array of slider elements
   * @param {number} count - Number of sliders to select
   * @returns {Array} Selected sliders
   */
  getRandomSliders(sliders, count) {
    const shuffled = [...sliders].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  /**
   * Enable correlation effects
   */
  enable() {
    this.isEnabled = true;
  }

  /**
   * Disable correlation effects
   */
  disable() {
    this.isEnabled = false;
  }

  /**
   * Set effect intensity
   * @param {number} intensity - Effect intensity (0-1)
   */
  setIntensity(intensity) {
    this.effectIntensity = Math.max(0, Math.min(1, intensity));
  }

  /**
   * Set effect duration
   * @param {number} duration - Duration in milliseconds
   */
  setDuration(duration) {
    this.effectDuration = Math.max(100, duration);
  }

  /**
   * Get correlation settings
   * @returns {Object} Current settings
   */
  getSettings() {
    return {
      enabled: this.isEnabled,
      intensity: this.effectIntensity,
      duration: this.effectDuration,
      activeEffects: this.activeEffects.size
    };
  }
}

// Add correlation pulse animation to CSS if not already present
if (!document.querySelector('#correlation-styles')) {
  const style = document.createElement('style');
  style.id = 'correlation-styles';
  style.textContent = `
    @keyframes correlationPulse {
      0% {
        opacity: 0;
        transform: scale(0.5);
      }
      50% {
        opacity: 1;
        transform: scale(1.2);
      }
      100% {
        opacity: 0.7;
        transform: scale(1);
      }
    }
  `;
  document.head.appendChild(style);
}
